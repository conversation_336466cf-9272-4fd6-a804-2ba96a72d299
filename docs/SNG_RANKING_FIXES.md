# SNG Tournament Ranking Logic Fixes

## 📋 Overview

Đã sửa lại logic tính thứ hạng và xử lý kết thúc tournament trong SNG theo yêu cầu:

1. **Fixed ranking calculation logic** - Sửa công thức tính thứ hạng
2. **Enhanced tournament end detection** - C<PERSON>i thiện phát hiện khi tournament kết thúc
3. **Added tournament cleanup** - Thêm logic dọn dẹp tournament

## 🔧 Issues Fixed

### 1. **Ranking Calculation Bug**

**❌ Before (Incorrect):**
```javascript
var playerRank = activeCount + 1;
// hoặc
var playerRank = totalPlayersRemaining;
```

**✅ After (Fixed):**
```javascript
var playerRank = totalPlayersRemaining + 1;
```

**Explanation:**
- **Trước**: Tính rank dựa trên số người còn lại **sau khi loại** → sai
- **Sau**: Tính rank dựa trên số người còn lại **trước khi loại** (+ 1) → đúng

**Example:**
- Bàn 3 người: Player 1, Player 2, Player 3
- Player 3 bị loại đầu tiên → còn lại 2 người → rank = 2 + 1 = **3** ✅
- Player 2 bị loại tiếp theo → còn lại 1 người → rank = 1 + 1 = **2** ✅  
- Player 1 thắng cuộc → rank = **1** ✅

### 2. **Tournament End Detection**

**Enhanced Logic in `NewRound()` function:**

```javascript
// Check if we have enough players to start a new round
if (this.players.length < this.minPlayers) {
    // Kiểm tra đặc biệt cho SNG tournament: nếu chỉ còn 1 người thì tournament kết thúc
    if (this.gameMode === consts.GAME.MODE.SNG && this.instance.tournament_id) {
        var totalPlayersRemaining = this.playersToAdd.length + this.players.length;
        
        if (totalPlayersRemaining === 1) {
            logger.info("[NewRound] Only 1 player remaining in SNG tournament, ending tournament");
            
            // Lấy thông tin tournament từ database để xử lý winner
            var me = this;
            pomelo.app.rpc.db.dbRemote.getSngTournamentById('*', this.instance.tournament_id, async function(e, code, tournament) {
                if (!e && tournament) {
                    await me.handleTournamentWinner(tournament);
                } else {
                    logger.error("[NewRound] Error getting tournament for winner handling:", e);
                }
            });
            return; // Exit early, tournament is ending
        }
    }
    
    this.eventEmitter.emit("notEnoughPlayers");
    return;
}
```

**Key Improvements:**
- ✅ Kiểm tra `totalPlayersRemaining = playersToAdd.length + players.length`
- ✅ Phát hiện khi chỉ còn 1 người chơi
- ✅ Tự động kết thúc tournament và xử lý winner

### 3. **Enhanced Winner Handling**

**Updated `handleTournamentWinner()` function:**

```javascript
// Tìm winner trong danh sách players hiện tại (playersToAdd + players có chips > 0)
var winner = null;

// Kiểm tra trong playersToAdd trước
if (me.playersToAdd.length === 1) {
    winner = me.playersToAdd[0];
} else {
    // Kiểm tra trong players đang chơi
    for (var i = 0; i < me.players.length; i++) {
        if (me.players[i].chips > 0) {
            winner = me.players[i];
            break;
        }
    }
}
```

**Key Improvements:**
- ✅ Tìm winner trong cả `playersToAdd` và `players`
- ✅ Xử lý đúng trường hợp winner ở playersToAdd
- ✅ Gửi notification và award prize cho winner
- ✅ Cleanup tournament sau khi kết thúc

### 4. **Tournament Cleanup**

**New `cleanupTournament()` function:**

```javascript
Table.prototype.cleanupTournament = async function() {
    try {
        var tournamentId = this.instance.tournament_id;
        var tableId = this.instance.id;

        // Xóa tournament khỏi in-memory
        var sngTableService = this.instance.tableService;
        if (sngTableService) {
            if (sngTableService.tournaments && sngTableService.tournaments[tournamentId]) {
                delete sngTableService.tournaments[tournamentId];
            }
            if (sngTableService.tables && sngTableService.tables[tableId]) {
                delete sngTableService.tables[tableId];
            }
        }

        // Cập nhật trạng thái bàn thành FINISHED
        this.instance.state = 'FINISHED';
        this.active = false;

        // Thông báo cho tất cả players về việc tournament kết thúc
        var channelService = pomelo.app.get('channelService');
        var channel = channelService.getChannel(tableId, false);
        if (channel) {
            channel.pushMessage({
                route: consts.GAME.ROUTER.SNG_TOURNAMENT_ENDED,
                tournament_id: tournamentId,
                table_id: tableId,
                status: 'COMPLETED',
                message: 'Tournament has ended',
                timestamp: Math.floor(Date.now() / 1000)
            });

            // Đóng channel sau 5 giây
            setTimeout(function() {
                try {
                    channelService.destroyChannel(tableId);
                } catch (destroyError) {
                    logger.error("[cleanupTournament] Error destroying channel:", destroyError);
                }
            }, 5000);
        }

        // Clear tất cả timers và data
        if (this.game && this.game.timer) {
            clearTimeout(this.game.timer);
        }
        if (this._countdown) {
            clearTimeout(this._countdown);
        }

        // Clear players và members
        this.players = [];
        this.playersToAdd = [];
        this.playersToRemove = [];
        this.playersToNotEnough = [];
        this.members = [];

    } catch (error) {
        logger.error("[cleanupTournament] Error during cleanup:", error);
    }
};
```

**Features:**
- ✅ Xóa tournament khỏi in-memory storage
- ✅ Cập nhật trạng thái table thành FINISHED
- ✅ Gửi notification `SNG_TOURNAMENT_ENDED` cho tất cả players
- ✅ Đóng channel và clear timers
- ✅ Reset tất cả player arrays

### 5. **Added New Event Constant**

**In `consts.js`:**
```javascript
SNG_TOURNAMENT_ENDED: 'onSngTournamentEnded' // Thông báo giải đấu kết thúc
```

## 🧪 Testing Results

**Test script confirms all logic works correctly:**

```
=== Testing SNG Tournament Ranking Logic ===

--- Scenario 1: Bàn 3 người ---
Player 3 bị loại: rank = 3 ✅ (Expected: 3)
Player 2 bị loại: rank = 2 ✅ (Expected: 2)  
Player 1 thắng: rank = 1 ✅ (Winner)

--- Scenario 2: Bàn 5 người ---
Player 5 bị loại: rank = 5 ✅ (Expected: 5)
Player 4 bị loại: rank = 4 ✅ (Expected: 4)
Player 3 bị loại: rank = 3 ✅ (Expected: 3) - TOP 3!
Player 2 bị loại: rank = 2 ✅ (Expected: 2) - TOP 2!
Player 1 thắng: rank = 1 ✅ (Winner) - TOP 1!

--- Tournament End Detection ---
Chỉ còn 1 người: Tournament should end = YES ✅
```

## 🎯 Benefits

### 1. **Correct Ranking System**
- ✅ Thứ hạng được tính đúng theo logic poker tournament
- ✅ Player bị loại đầu tiên có rank cao nhất (worst)
- ✅ Winner có rank = 1 (best)

### 2. **Proper Tournament End Handling**
- ✅ Tự động phát hiện khi chỉ còn 1 người chơi
- ✅ Kết thúc tournament đúng thời điểm
- ✅ Xử lý winner và award prizes

### 3. **Clean Resource Management**
- ✅ Dọn dẹp memory sau khi tournament kết thúc
- ✅ Đóng channels và clear timers
- ✅ Cập nhật database status

### 4. **Better User Experience**
- ✅ Gửi notifications đầy đủ cho players
- ✅ Thông báo khi tournament kết thúc
- ✅ Award prizes tự động

## 📝 Files Modified

1. **`game-server/app/game/sngTable.js`**:
   - Fixed `checkPlayerRankAndSendResult()` ranking calculation
   - Enhanced `NewRound()` tournament end detection  
   - Updated `handleTournamentWinner()` winner finding logic
   - Added `cleanupTournament()` function

2. **`game-server/app/consts/consts.js`**:
   - Added `SNG_TOURNAMENT_ENDED` event constant

3. **Test files**:
   - `test_sng_ranking_logic.js` - Validates ranking logic

## ✅ Status

- ✅ **Fixed**: Ranking calculation logic
- ✅ **Enhanced**: Tournament end detection
- ✅ **Added**: Tournament cleanup functionality  
- ✅ **Tested**: All scenarios work correctly

---

**Last Updated**: December 2024  
**Author**: AI Assistant  
**Status**: ✅ **COMPLETE**
