/**
 * Test script để kiểm tra các fixes cho SNG tournament
 */

console.log("=== Testing SNG Tournament Fixes ===\n");

// Test 1: Reward calculation
function testRewardCalculation() {
    console.log("--- Test 1: Reward Calculation ---");
    
    // Mock tournament data
    var tournament1 = {
        id: 390,
        buy_in: 1000000,
        player_capacity: 3,
        reward_pool: 0 // Trường hợp reward_pool = 0
    };
    
    var tournament2 = {
        id: 391,
        buy_in: 1000000,
        player_capacity: 3,
        reward_pool: 3000000 // Trường hợp đã có reward_pool
    };
    
    // Mock reward distribution
    var rewardDistribution = {
        first_place: 50,   // 50%
        second_place: 30,  // 30%
        third_place: 20    // 20%
    };
    
    function calculateRewardAmount(tournament, rank) {
        if (!tournament || rank > 3) {
            return 0;
        }

        // Tính reward_pool nếu chưa có hoặc bằng 0
        var rewardPool = tournament.reward_pool;
        if (!rewardPool || rewardPool === 0) {
            // reward_pool = buy_in * player_capacity (không tính fee)
            rewardPool = tournament.buy_in * tournament.player_capacity;
            console.log("   Calculated reward_pool:", rewardPool);
        }

        var percentage = 0;
        switch (rank) {
            case 1:
                percentage = rewardDistribution.first_place;
                break;
            case 2:
                percentage = rewardDistribution.second_place;
                break;
            case 3:
                percentage = rewardDistribution.third_place;
                break;
            default:
                percentage = 0;
        }

        return Math.floor((rewardPool * percentage) / 100);
    }
    
    console.log("Tournament 1 (reward_pool = 0):");
    console.log("  Buy-in:", tournament1.buy_in.toLocaleString());
    console.log("  Player capacity:", tournament1.player_capacity);
    console.log("  Expected reward_pool:", (tournament1.buy_in * tournament1.player_capacity).toLocaleString());
    console.log("  Rank 1 reward:", calculateRewardAmount(tournament1, 1).toLocaleString(), "(Expected: 1,500,000)");
    console.log("  Rank 2 reward:", calculateRewardAmount(tournament1, 2).toLocaleString(), "(Expected: 900,000)");
    console.log("  Rank 3 reward:", calculateRewardAmount(tournament1, 3).toLocaleString(), "(Expected: 600,000)");
    
    console.log("\nTournament 2 (reward_pool = 3,000,000):");
    console.log("  Rank 1 reward:", calculateRewardAmount(tournament2, 1).toLocaleString(), "(Expected: 1,500,000)");
    console.log("  Rank 2 reward:", calculateRewardAmount(tournament2, 2).toLocaleString(), "(Expected: 900,000)");
    console.log("  Rank 3 reward:", calculateRewardAmount(tournament2, 3).toLocaleString(), "(Expected: 600,000)");
}

// Test 2: Player count logic
function testPlayerCountLogic() {
    console.log("\n--- Test 2: Player Count Logic ---");
    
    // Scenario 1: 3 người chơi, 1 người bị loại
    console.log("Scenario 1: 3 người chơi, player 3 bị loại");
    var playersToAdd = [];
    var players = [
        { id: 1, chips: 5000000, folded: false },
        { id: 2, chips: 3000000, folded: false },
        { id: 3, chips: 0, folded: true } // Bị loại
    ];
    
    var playersInTable = playersToAdd.length;
    var playersInGame = 0;
    for (var i = 0; i < players.length; i++) {
        if (players[i].chips > 0) {
            playersInGame++;
        }
    }
    var totalPlayersRemaining = playersInTable + playersInGame;
    var playerRank = totalPlayersRemaining + 1; // Rank của player 3
    
    console.log("  playersToAdd:", playersInTable);
    console.log("  playersInGame (chips > 0):", playersInGame);
    console.log("  totalPlayersRemaining:", totalPlayersRemaining);
    console.log("  Player 3 rank:", playerRank, "(Expected: 3)");
    console.log("  Should call handleTournamentWinner?", totalPlayersRemaining === 1 ? "NO" : "YES");
    
    // Scenario 2: Chỉ còn 1 người
    console.log("\nScenario 2: Chỉ còn 1 người chơi");
    playersToAdd = [];
    players = [
        { id: 1, chips: 10000000, folded: false }, // Winner
        { id: 2, chips: 0, folded: true }, // Bị loại
        { id: 3, chips: 0, folded: true }  // Bị loại
    ];
    
    playersInTable = playersToAdd.length;
    playersInGame = 0;
    for (var i = 0; i < players.length; i++) {
        if (players[i].chips > 0) {
            playersInGame++;
        }
    }
    totalPlayersRemaining = playersInTable + playersInGame;
    
    console.log("  playersToAdd:", playersInTable);
    console.log("  playersInGame (chips > 0):", playersInGame);
    console.log("  totalPlayersRemaining:", totalPlayersRemaining);
    console.log("  Should call handleTournamentWinner?", totalPlayersRemaining === 1 ? "YES" : "NO");
    
    // Scenario 3: Winner trong playersToAdd
    console.log("\nScenario 3: Winner trong playersToAdd");
    playersToAdd = [
        { id: 1, chips: 10000000, folded: false } // Winner
    ];
    players = [
        { id: 2, chips: 0, folded: true }, // Bị loại
        { id: 3, chips: 0, folded: true }  // Bị loại
    ];
    
    playersInTable = playersToAdd.length;
    playersInGame = 0;
    for (var i = 0; i < players.length; i++) {
        if (players[i].chips > 0) {
            playersInGame++;
        }
    }
    totalPlayersRemaining = playersInTable + playersInGame;
    
    console.log("  playersToAdd:", playersInTable);
    console.log("  playersInGame (chips > 0):", playersInGame);
    console.log("  totalPlayersRemaining:", totalPlayersRemaining);
    console.log("  Should call handleTournamentWinner?", totalPlayersRemaining === 1 ? "YES" : "NO");
}

// Test 3: Response structure
function testResponseStructure() {
    console.log("\n--- Test 3: Response Structure ---");
    
    // Mock tournament players data
    var allTournamentPlayers = [
        {
            player_id: 6,
            player_name: "dvminh",
            seat_number: 1,
            initial_chips: 100000000,
            current_chips: 0,
            status: "ELIMINATED",
            eliminated_at_hand: 5,
            rank: 3,
            avatar: "https://s3.pokee.club/avatar/A29.png"
        },
        {
            player_id: 7,
            player_name: "dvminh3",
            seat_number: 2,
            initial_chips: 100000000,
            current_chips: 0,
            status: "ELIMINATED",
            eliminated_at_hand: 8,
            rank: 2,
            avatar: "https://s3.pokee.club/avatar/A22.png"
        },
        {
            player_id: 8,
            player_name: "winner",
            seat_number: 3,
            initial_chips: 100000000,
            current_chips: 300000000,
            status: "WINNER",
            eliminated_at_hand: 0,
            rank: 1,
            avatar: "https://s3.pokee.club/avatar/A30.png"
        }
    ];
    
    var tournament = {
        id: 390,
        buy_in: 1000000,
        player_capacity: 3,
        reward_pool: 0
    };
    
    var rewardPool = tournament.reward_pool || (tournament.buy_in * tournament.player_capacity);
    
    var sngResultData = {
        tournament: {
            id: tournament.id,
            tournament_id: tournament.id,
            reward_pool: rewardPool // Fixed reward_pool
        },
        players: allTournamentPlayers.map(function(player) {
            return {
                id: player.player_id,
                player_id: player.player_id,
                player_name: player.player_name,
                seat_number: player.seat_number,
                initial_chips: player.initial_chips,
                current_chips: player.current_chips,
                status: player.status,
                eliminated_at_hand: player.eliminated_at_hand,
                rank: player.rank,
                avatar: player.avatar
            };
        }),
        rewards: { // Changed from array to object
            tournament_id: tournament.id,
            player_id: 8, // Winner
            rank: 1,
            reward_amount: Math.floor((rewardPool * 50) / 100), // 50% for rank 1
            player_name: "winner",
            avatar: "https://s3.pokee.club/avatar/A30.png"
        }
    };
    
    console.log("Fixed response structure:");
    console.log("  Tournament reward_pool:", sngResultData.tournament.reward_pool.toLocaleString());
    console.log("  Players count:", sngResultData.players.length);
    console.log("  Players details:");
    sngResultData.players.forEach(function(player) {
        console.log("    -", player.player_name, "rank:", player.rank, "status:", player.status);
    });
    console.log("  Rewards structure:", typeof sngResultData.rewards);
    console.log("  Winner reward amount:", sngResultData.rewards.reward_amount.toLocaleString());
}

// Run tests
testRewardCalculation();
testPlayerCountLogic();
testResponseStructure();

console.log("\n=== Test Completed ===");
