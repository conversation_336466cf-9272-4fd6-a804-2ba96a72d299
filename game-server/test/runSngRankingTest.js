/**
 * Test runner for SNG Tournament Ranking functionality
 */

const { models } = require('../app/models');
const MockSngTable = require('./sngRankingTest');
const moment = require('moment');

// Test data
const testTournament = {
    code: 'SNG_RANK_TEST_' + moment().format('YYYYMMDD_HHmmss'),
    status: 'IN_PROGRESS',
    player_capacity: 5,
    buy_in: 50000000,
    fee: 5000000,
    reward_pool: 250000000, // 5 players * 50M buy_in
    started_at: new Date()
};

const testPlayers = [
    { id: 101, nick_name: 'TestPlayer1', balance: 1000000000, avatar: 'avatar1.png' },
    { id: 102, nick_name: 'TestPlayer2', balance: 1000000000, avatar: 'avatar2.png' },
    { id: 103, nick_name: 'TestPlayer3', balance: 1000000000, avatar: 'avatar3.png' },
    { id: 104, nick_name: 'TestPlayer4', balance: 1000000000, avatar: 'avatar4.png' },
    { id: 105, nick_name: 'TestPlayer5', balance: 1000000000, avatar: 'avatar5.png' }
];

async function setupTestData() {
    console.log('=== Setting up test data ===');
    
    try {
        // Create test tournament
        const tournament = await models.SngTournament.create(testTournament);
        console.log('✓ Tournament created:', tournament.id);
        
        // Create or update test players
        const players = [];
        for (const playerData of testPlayers) {
            let player = await models.Player.findByPk(playerData.id);
            
            if (!player) {
                player = await models.Player.create(playerData);
                console.log('✓ Player created:', player.id);
            } else {
                await player.update(playerData);
                console.log('✓ Player updated:', player.id);
            }
            
            players.push(player);
        }
        
        // Register players for tournament
        for (let i = 0; i < players.length; i++) {
            const player = players[i];
            const status = i < 2 ? 'ACTIVE' : 'ELIMINATED'; // First 2 players are still active
            const rank = status === 'ELIMINATED' ? (5 - i) : 0; // Ranks 3, 4, 5 for eliminated players
            
            await models.SngTournamentPlayer.create({
                tournament_id: tournament.id,
                player_id: player.id,
                initial_chips: 100000000,
                current_chips: status === 'ACTIVE' ? 50000000 : 0,
                status: status,
                rank: rank,
                joined_at: new Date(),
                eliminated_at_hand: status === 'ELIMINATED' ? 10 : null
            });
            
            console.log(`✓ Player ${player.id} registered with status: ${status}, rank: ${rank}`);
        }
        
        return { tournament, players };
    } catch (error) {
        console.error('Error setting up test data:', error);
        throw error;
    }
}

async function testRankingLogic(tournament, players) {
    console.log('\n=== Testing Ranking Logic ===');
    
    try {
        // Create mock SNG table
        const mockTable = new MockSngTable(tournament.id);
        
        // Test scenarios:
        // 1. Player eliminated at rank 3 (should get notification + prize)
        // 2. Player eliminated at rank 4 (should NOT get notification)
        // 3. Player eliminated at rank 2 (should get notification + prize)
        // 4. Player eliminated at rank 1 (should get notification + prize)
        
        console.log('\n--- Test Case 1: Player eliminated at rank 3 ---');
        // Simulate player 3 (ID: 103) being eliminated when 2 players are still active
        // This should result in rank 3 (2 active + 1 = 3)
        await mockTable.checkPlayerRankAndSendResult(103, 'TestPlayer3', 2, players[2]);
        
        console.log('\n--- Test Case 2: Player eliminated at rank 4 ---');
        // Simulate player 4 (ID: 104) being eliminated when 3 players were still active
        // This should result in rank 4 (3 active + 1 = 4) - NO notification expected
        // First, we need to simulate 3 active players by updating the database
        await models.SngTournamentPlayer.update(
            { status: 'ACTIVE', current_chips: 30000000 },
            { where: { tournament_id: tournament.id, player_id: 103 } }
        );
        
        await mockTable.checkPlayerRankAndSendResult(104, 'TestPlayer4', 3, players[3]);
        
        // Reset player 3 to eliminated for next tests
        await models.SngTournamentPlayer.update(
            { status: 'ELIMINATED', current_chips: 0, rank: 3 },
            { where: { tournament_id: tournament.id, player_id: 103 } }
        );
        
        console.log('\n--- Test Case 3: Player eliminated at rank 2 ---');
        // Simulate player 2 (ID: 102) being eliminated when 1 player is still active
        // This should result in rank 2 (1 active + 1 = 2)
        await models.SngTournamentPlayer.update(
            { status: 'ELIMINATED', current_chips: 0 },
            { where: { tournament_id: tournament.id, player_id: 102 } }
        );
        
        await mockTable.checkPlayerRankAndSendResult(102, 'TestPlayer2', 1, players[1]);
        
        console.log('\n--- Test Case 4: Player eliminated at rank 1 (Winner) ---');
        // Simulate player 1 (ID: 101) being the last player (winner)
        // This should result in rank 1 (0 active + 1 = 1)
        await models.SngTournamentPlayer.update(
            { status: 'ELIMINATED', current_chips: 0 },
            { where: { tournament_id: tournament.id, player_id: 101 } }
        );
        
        await mockTable.checkPlayerRankAndSendResult(101, 'TestPlayer1', 0, players[0]);
        
        console.log('\n✓ All ranking tests completed');
        
    } catch (error) {
        console.error('Error testing ranking logic:', error);
        throw error;
    }
}

async function testRewardCalculation(tournament) {
    console.log('\n=== Testing Reward Calculation ===');
    
    try {
        const mockTable = new MockSngTable(tournament.id);
        
        // Test reward calculation for different ranks
        const rank1Reward = mockTable.calculateRewardAmount(tournament, 1);
        const rank2Reward = mockTable.calculateRewardAmount(tournament, 2);
        const rank3Reward = mockTable.calculateRewardAmount(tournament, 3);
        const rank4Reward = mockTable.calculateRewardAmount(tournament, 4);
        
        console.log('Reward calculations:');
        console.log(`- Rank 1 (50%): ${rank1Reward.toLocaleString()} chips`);
        console.log(`- Rank 2 (30%): ${rank2Reward.toLocaleString()} chips`);
        console.log(`- Rank 3 (20%): ${rank3Reward.toLocaleString()} chips`);
        console.log(`- Rank 4 (0%): ${rank4Reward.toLocaleString()} chips`);
        
        // Verify calculations
        const expectedRank1 = Math.floor(tournament.reward_pool * 0.5);
        const expectedRank2 = Math.floor(tournament.reward_pool * 0.3);
        const expectedRank3 = Math.floor(tournament.reward_pool * 0.2);
        
        console.log('\nVerification:');
        console.log(`✓ Rank 1: ${rank1Reward === expectedRank1 ? 'PASS' : 'FAIL'}`);
        console.log(`✓ Rank 2: ${rank2Reward === expectedRank2 ? 'PASS' : 'FAIL'}`);
        console.log(`✓ Rank 3: ${rank3Reward === expectedRank3 ? 'PASS' : 'FAIL'}`);
        console.log(`✓ Rank 4: ${rank4Reward === 0 ? 'PASS' : 'FAIL'}`);
        
    } catch (error) {
        console.error('Error testing reward calculation:', error);
        throw error;
    }
}

async function cleanupTestData(tournament) {
    console.log('\n=== Cleaning up test data ===');
    
    try {
        // Delete tournament players
        await models.SngTournamentPlayer.destroy({
            where: { tournament_id: tournament.id }
        });
        console.log('✓ Tournament players deleted');
        
        // Delete tournament
        await models.SngTournament.destroy({
            where: { id: tournament.id }
        });
        console.log('✓ Tournament deleted');
        
        // Delete test players
        await models.Player.destroy({
            where: { id: testPlayers.map(p => p.id) }
        });
        console.log('✓ Test players deleted');
        
    } catch (error) {
        console.error('Error cleaning up test data:', error);
        // Don't throw error during cleanup
    }
}

// Main test runner
async function runTest() {
    console.log('🚀 Starting SNG Ranking Test');
    console.log('=====================================');
    
    let tournament, players;
    
    try {
        // Setup test data
        const testData = await setupTestData();
        tournament = testData.tournament;
        players = testData.players;
        
        // Test reward calculation
        await testRewardCalculation(tournament);
        
        // Test ranking logic
        await testRankingLogic(tournament, players);
        
        console.log('\n🎉 All tests completed successfully!');
        
    } catch (error) {
        console.error('\n❌ Test failed:', error);
    } finally {
        // Cleanup
        if (tournament) {
            await cleanupTestData(tournament);
        }
        
        // Close database connection
        await models.sequelize.close();
        console.log('\n✓ Database connection closed');
    }
}

// Run the test
if (require.main === module) {
    runTest();
}

module.exports = { runTest };
