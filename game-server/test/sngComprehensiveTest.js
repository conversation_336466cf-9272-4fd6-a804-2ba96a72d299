/**
 * Comprehensive test for SNG Tournament functionality
 * 
 * Tests all the new features:
 * 1. Top 1, 2, 3 ranking and rewards
 * 2. Database logging
 * 3. Player status updates
 * 4. Tournament registration with database
 */

const consts = require('../app/consts/consts');
const CODE = require('../app/consts/code');
const sngTournaments = require('../config/data/sngTournaments.json');

// Mock data
const mockTournament = {
    id: 1,
    code: 'SNG_COMPREHENSIVE_TEST',
    status: 'IN_PROGRESS',
    player_capacity: 5,
    buy_in: 50000000,
    fee: 5000000,
    reward_pool: 250000000,
    created_at: new Date(),
    started_at: new Date(),
    ended_at: null
};

const mockPlayers = [
    { id: 201, nick_name: 'Winner', avatar: 'avatar1.png', balance: 1000000000 },
    { id: 202, nick_name: 'Runner-up', avatar: 'avatar2.png', balance: 1000000000 },
    { id: 203, nick_name: 'Third', avatar: 'avatar3.png', balance: 1000000000 },
    { id: 204, nick_name: 'Fourth', avatar: 'avatar4.png', balance: 1000000000 },
    { id: 205, nick_name: 'Fifth', avatar: 'avatar5.png', balance: 1000000000 }
];

// Mock services
const mockServices = {
    messageService: {
        pushMessageByUid: async function(uid, route, data, code) {
            console.log(`📨 Message sent to player ${uid}:`);
            console.log(`   Route: ${route}`);
            console.log(`   Code: ${code}`);
            if (data.tournament) {
                console.log(`   Tournament: ${data.tournament.id}`);
            }
            if (data.players && data.players[0]) {
                console.log(`   Player Rank: ${data.players[0].rank}`);
            }
            if (data.rewards && data.rewards[0]) {
                console.log(`   Reward: ${data.rewards[0].reward_amount?.toLocaleString()} chips`);
            }
            return Promise.resolve();
        }
    },
    
    pomelo: {
        app: {
            rpc: {
                db: {
                    dbRemote: {
                        getSngTournamentById: function(serverId, tournamentId, callback) {
                            console.log(`🔍 Getting tournament: ${tournamentId}`);
                            callback(null, CODE.OK, mockTournament);
                        },
                        
                        countActiveSngTournamentPlayers: function(serverId, tournamentId, callback) {
                            // Simulate different scenarios
                            const scenarios = {
                                'test_rank_3': 2, // 2 active + 1 eliminated = rank 3
                                'test_rank_2': 1, // 1 active + 1 eliminated = rank 2
                                'test_rank_1': 0  // 0 active + 1 eliminated = rank 1 (winner)
                            };
                            
                            const activeCount = scenarios[global.currentTestScenario] || 2;
                            console.log(`📊 Active players count: ${activeCount}`);
                            callback(null, CODE.OK, activeCount);
                        },
                        
                        updateSngTournamentPlayer: function(serverId, payload, callback) {
                            console.log(`💾 Updating player status:`, {
                                player_id: payload.player_id,
                                status: payload.status,
                                rank: payload.rank,
                                current_chips: payload.current_chips
                            });
                            callback(null, CODE.OK, { affected_rows: 1 });
                        },
                        
                        createSngTournamentLog: function(serverId, payload, callback) {
                            console.log(`📝 Creating tournament log:`, {
                                player_id: payload.player_id,
                                action_type: payload.action_type,
                                amount: payload.amount
                            });
                            callback(null, CODE.OK, { id: Date.now() });
                        },
                        
                        updateSngTournament: function(serverId, payload, callback) {
                            console.log(`🏆 Updating tournament status:`, {
                                tournament_id: payload.tournament_id,
                                status: payload.status
                            });
                            callback(null, CODE.OK, { affected_rows: 1 });
                        },
                        
                        updatePlayerBalance: function(serverId, playerId, amount, description, callback) {
                            console.log(`💰 Updating player balance:`, {
                                player_id: playerId,
                                amount: amount.toLocaleString(),
                                description: description
                            });
                            
                            const player = mockPlayers.find(p => p.id === playerId);
                            if (player) {
                                player.balance += amount;
                                callback(null, CODE.OK, {
                                    balance: player.balance,
                                    old_balance: player.balance - amount,
                                    new_balance: player.balance
                                });
                            } else {
                                callback('Player not found', CODE.NOT_FOUND, null);
                            }
                        },
                        
                        getPlayerById: function(serverId, playerId, callback) {
                            const player = mockPlayers.find(p => p.id === playerId);
                            if (player) {
                                callback(null, CODE.OK, player);
                            } else {
                                callback('Player not found', CODE.NOT_FOUND, null);
                            }
                        }
                    }
                },
                manager: {
                    userRemote: {
                        incrUserCachedBalance: function(serverId, playerId, amount, callback) {
                            console.log(`🔄 Updating userCached balance:`, {
                                player_id: playerId,
                                amount: amount.toLocaleString()
                            });
                            
                            const player = mockPlayers.find(p => p.id === playerId);
                            if (player) {
                                callback(null, { id: playerId, balance: player.balance + amount });
                            } else {
                                callback('Player not found', null);
                            }
                        }
                    }
                }
            }
        }
    }
};

// Test class with comprehensive SNG logic
class ComprehensiveSngTester {
    constructor() {
        this.gameMode = consts.GAME.MODE.SNG;
        this.instance = {
            tournament_id: mockTournament.id
        };
        
        // Mock global objects
        global.messageService = mockServices.messageService;
        global.pomelo = mockServices.pomelo;
    }

    // Import all the methods from sngTable.js
    calculateRewardAmount(tournament, rank) {
        if (!tournament || !tournament.reward_pool || rank > 3) {
            return 0;
        }

        var rewardDistribution = sngTournaments.reward_distribution;
        var percentage = 0;

        switch (rank) {
            case 1:
                percentage = rewardDistribution.first_place;
                break;
            case 2:
                percentage = rewardDistribution.second_place;
                break;
            case 3:
                percentage = rewardDistribution.third_place;
                break;
            default:
                percentage = 0;
        }

        return Math.floor((tournament.reward_pool * percentage) / 100);
    }

    async sendTournamentResult(uid, tournament, rank, rewardAmount, playerInfo) {
        try {
            var sngCmdPushResult = consts.GAME.ROUTER.SNG_TOURNAMENT_RESULT;
            var sngResultData = {
                tournament: {
                    id: tournament.id,
                    tournament_id: tournament.id,
                    code: tournament.code,
                    status: tournament.status,
                    player_capacity: tournament.player_capacity,
                    buy_in: tournament.buy_in,
                    fee: tournament.fee,
                    reward_pool: tournament.reward_pool,
                    created_at: tournament.created_at,
                    started_at: tournament.started_at,
                    ended_at: tournament.ended_at
                },
                players: [{
                    id: uid,
                    player_id: uid,
                    player_name: playerInfo.nick_name || playerInfo.display_name || 'Player',
                    seat_number: 0,
                    initial_chips: 100000000,
                    current_chips: rank === 1 ? 500000000 : 0, // Winner has chips, others eliminated
                    status: rank === 1 ? "WINNER" : "ELIMINATED",
                    eliminated_at_hand: 0,
                    rank: rank,
                    avatar: playerInfo.avatar || ''
                }],
                rewards: [{
                    tournament_id: tournament.id,
                    player_id: uid,
                    rank: rank,
                    reward_amount: rewardAmount,
                    player_name: playerInfo.nick_name || playerInfo.display_name || 'Player',
                    avatar: playerInfo.avatar || ''
                }]
            };
            
            var sngCode = CODE.SNG.TOURNAMENT_RESULT;
            await mockServices.messageService.pushMessageByUid(uid, sngCmdPushResult, sngResultData, sngCode);
            
        } catch (error) {
            console.error("❌ Error sending tournament result:", error);
        }
    }

    async awardPlayerPrize(uid, rewardAmount, rank) {
        try {
            // Update database balance
            mockServices.pomelo.app.rpc.db.dbRemote.updatePlayerBalance('*', uid, rewardAmount,
                'SNG Tournament Prize - Position ' + rank, function(updateErr, updateCode, updateResult) {
                if (!updateErr && updateResult) {
                    console.log(`✅ Prize awarded successfully to player ${uid}`);

                    // Update userCached balance
                    mockServices.pomelo.app.rpc.manager.userRemote.incrUserCachedBalance(null, uid, rewardAmount, function (error, user) {
                        if (!error && user) {
                            console.log(`✅ UserCached balance updated for player ${uid}`);
                        } else {
                            console.error(`❌ Error updating userCached balance for player ${uid}:`, error);
                        }
                    });

                    // Send balance update notification
                    var _cmdPush = consts.GAME.ROUTER.UPDATE_MYSELF;
                    var _arrMsg = {
                        type: CODE.USER.BALANCE,
                        msg: "SNG Tournament Prize Awarded",
                        balance: updateResult.balance,
                        prize_amount: rewardAmount,
                        rank: rank
                    };
                    var _code = CODE.USER.UPDATE_MONEY;
                    mockServices.messageService.pushMessageByUid(uid, _cmdPush, _arrMsg, _code);
                    
                } else {
                    console.error(`❌ Error updating player balance for player ${uid}:`, updateErr);
                }
            });
            
        } catch (error) {
            console.error(`❌ Error awarding prize to player ${uid}:`, error);
        }
    }

    async updatePlayerStatus(uid, rank, currentChips) {
        try {
            var status = rank === 1 ? 'WINNER' : 'ELIMINATED';
            var payload = {
                tournament_id: this.instance.tournament_id,
                player_id: uid,
                status: status,
                rank: rank,
                current_chips: currentChips,
                eliminated_at_hand: 0
            };

            mockServices.pomelo.app.rpc.db.dbRemote.updateSngTournamentPlayer('*', payload, function(updateErr, updateCode, updateResult) {
                if (!updateErr && updateResult) {
                    console.log(`✅ Player status updated for player ${uid}`);
                } else {
                    console.error(`❌ Error updating player status for player ${uid}:`, updateErr);
                }
            });
        } catch (error) {
            console.error(`❌ Error updating player status for player ${uid}:`, error);
        }
    }

    async logTournamentAction(uid, actionType, data, amount) {
        try {
            var payload = {
                tournament_id: this.instance.tournament_id,
                player_id: uid,
                action_type: actionType,
                data: data,
                amount: amount || 0
            };

            mockServices.pomelo.app.rpc.db.dbRemote.createSngTournamentLog('*', payload, function(logErr, logCode, logResult) {
                if (!logErr && logResult) {
                    console.log(`✅ Tournament action logged for player ${uid}`);
                } else {
                    console.error(`❌ Error logging tournament action for player ${uid}:`, logErr);
                }
            });
        } catch (error) {
            console.error(`❌ Error logging tournament action for player ${uid}:`, error);
        }
    }

    async updateTournamentStatus(status, endedAt) {
        try {
            var payload = {
                tournament_id: this.instance.tournament_id,
                status: status
            };

            if (endedAt) {
                payload.ended_at = endedAt;
            }

            mockServices.pomelo.app.rpc.db.dbRemote.updateSngTournament('*', payload, function(updateErr, updateCode, updateResult) {
                if (!updateErr && updateResult) {
                    console.log(`✅ Tournament status updated to: ${status}`);
                } else {
                    console.error(`❌ Error updating tournament status:`, updateErr);
                }
            });
        } catch (error) {
            console.error(`❌ Error updating tournament status:`, error);
        }
    }

    // Main test method that simulates the full ranking logic
    async testComprehensiveRanking(uid, rank, playerInfo, scenario) {
        console.log(`\n🎯 Testing comprehensive ranking for player ${uid} - Rank ${rank}`);
        console.log(`📋 Scenario: ${scenario}`);
        
        // Set global scenario for mock services
        global.currentTestScenario = scenario;
        
        // Get tournament info
        mockServices.pomelo.app.rpc.db.dbRemote.getSngTournamentById('*', this.instance.tournament_id, async (e, code, tournament) => {
            if (e || !tournament) {
                console.error("❌ Error getting tournament:", e);
                return;
            }

            // Count active players
            mockServices.pomelo.app.rpc.db.dbRemote.countActiveSngTournamentPlayers('*', this.instance.tournament_id, async (e, code, activeCount) => {
                if (e) {
                    console.error("❌ Error counting active players:", e);
                    return;
                }

                // Calculate rank
                var playerRank = activeCount + 1;
                console.log(`📊 Calculated rank: ${playerRank} (${activeCount} active players remaining)`);

                // Update player status
                await this.updatePlayerStatus(uid, playerRank, rank === 1 ? 500000000 : 0);

                // Log action
                await this.logTournamentAction(uid, rank === 1 ? 'TOURNAMENT_WINNER' : 'PLAYER_ELIMINATED', {
                    rank: playerRank,
                    active_players_remaining: activeCount,
                    player_name: playerInfo.nick_name
                }, 0);

                // Process rewards for top 3
                if (playerRank <= 3) {
                    var rewardAmount = this.calculateRewardAmount(tournament, playerRank);
                    
                    console.log(`🏆 Player achieved top 3 - Rank ${playerRank}, Reward: ${rewardAmount.toLocaleString()} chips`);

                    // Send tournament result
                    await this.sendTournamentResult(uid, tournament, playerRank, rewardAmount, playerInfo);

                    // Award prize
                    if (rewardAmount > 0) {
                        await this.awardPlayerPrize(uid, rewardAmount, playerRank);
                    }
                }

                // If winner (rank 1), update tournament status
                if (playerRank === 1) {
                    await this.updateTournamentStatus('COMPLETED', new Date());
                }

                console.log(`✅ Comprehensive ranking test completed for player ${uid}`);
            });
        });
    }
}

module.exports = ComprehensiveSngTester;
