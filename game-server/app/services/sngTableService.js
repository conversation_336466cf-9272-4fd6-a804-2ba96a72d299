var events = require('events');
var uuid = require('node-uuid');
var logger = require('pomelo-logger').getLogger('game-log', __filename);
var TableStore = require('../../app/persistence/tables');
var logsGameDao = require('../dao/logsGameDao');

var UserStore = require('../../app/persistence/users');
var userDao = require('../dao/userDao');

var dispatcher = require('../util/dispatcher');
var SngTable = require('../game/sngTable');

var _ = require('underscore');
var schedule = require('pomelo-scheduler');
var consts = require('../consts/consts');
var CODE = require('../consts/code');

var rabbitConn = require('../dao/rabbitmq/rabbitmq');
var utils = require('../util/utils');
var sngTournaments = require('../../config/data/sngTournaments.json');

var async = require('async');
const pomelo = require('pomelo');

/**
 * Create and maintain SNG tournament tables.
 * SngTableService is created by sngTableComponent.
 */
var SngTableService = function(app, opts) {
    opts = opts || {};
    this.app = app;
    this.tables = {}; // Active tournament tables
    this.waitingTournaments = {}; // Waiting tournaments (one per type/level)
    this.tournaments = {}; // Active tournament data storage
    this.prefix = opts.prefix;
    this.store = opts.store;
    this.stateService = this.app.get('stateService');

    this.dbService = this.app.get('dbService');
    
    // Initialize tournament system
    this.init();
};

module.exports = SngTableService;

/**
 * Initialize SNG tournament system
 */
SngTableService.prototype.init = function() {
    logger.info("---> Khoi tao SNG Tournament Service");
    
    // Initialize waiting tournaments for each type/level combination
    this.autoInitTournamentsPerType();
};

/**
 * Auto initialize one waiting tournament per type/level combination
 * Similar to autoInitTablesPerZone in tableService
 * Chỉ tạo in-memory tournaments, không lưu vào database ngay
 */
SngTableService.prototype.autoInitTournamentsPerType = function() {
    var self = this;

    logger.info("---> Auto init SNG tournaments per type/level (in-memory only)");

    // Initialize one waiting tournament for each configuration
    sngTournaments.tournaments.forEach(function(config) {
        var tableType = config.player_capacity === 5 ? '5_PLAYERS' :
                       (config.player_capacity === 9 ? '9_PLAYERS' : '3_PLAYERS'); // Added 3_PLAYERS for testing
        var level = config.level;
        var tournamentKey = tableType + '_' + level;

        // Create waiting tournament (in-memory only)
        self.createInMemoryWaitingTournament(config, tableType, level);
        logger.info("Created in-memory waiting tournament for", tournamentKey);
    });
};

/**
 * Create an in-memory waiting tournament for a specific type/level
 * Không lưu vào database ngay, chỉ tạo structure trong memory
 */
SngTableService.prototype.createInMemoryWaitingTournament = function(config, tableType, level) {
    var self = this;
    var tournamentKey = tableType + '_' + level;

    // Check if waiting tournament already exists
    if (self.waitingTournaments[tournamentKey]) {
        return self.waitingTournaments[tournamentKey];
    }

    var tournamentId = uuid.v1();
    var tableId = uuid.v1();

    logger.info("[createInMemoryWaitingTournament] Creating in-memory waiting tournament:", tournamentKey, "tournament_id:", tournamentId);

    // Create tournament data for memory only (no database yet)
    var tournament = {
        id: tournamentId, // Temporary ID, will be replaced when saved to database
        table_type: tableType,
        level: level,
        config: config,
        players: [], // Empty initially
        status: 'waiting',
        created_at: Date.now(),
        started_at: null,
        finished_at: null,
        current_blind_level: 0,
        blind_schedule_job: null,
        elimination_log: [],
        registered_count: 0,
        db_tournament: null // Will be set when saved to database
    };

    // Store as waiting tournament
    self.waitingTournaments[tournamentKey] = tournament;

    // Create table object for this waiting tournament
    var tableObj = {
        id: tableId,
        tournament_id: tournamentId, // Use temporary ID
        creator: null, // Will be set when first player joins
        state: 'WAITING',
        tableService: self
    };

    // Create SNG table instance with initial blind level
    var initialBlind = sngTournaments.blind_structure[0];
    tableObj.table = new SngTable(
        initialBlind.small_blind,
        initialBlind.big_blind,
        2, // minPlayers
        config.player_capacity,
        config.initial_chips,
        config.initial_chips,
        'sng', // gameMode
        tableObj
    );

    // Set tournament-specific properties
    tableObj.table.tournament_id = tournamentId; // Use temporary ID
    tableObj.table.blind_structure = sngTournaments.blind_structure;
    tableObj.table.current_blind_level = 0;
    tableObj.table.payout_structure = sngTournaments.reward_distribution;
    tableObj.table.ante = initialBlind.ante || 0;

    // Initialize members array
    tableObj.table.members = [];

    self.tables[tableId] = tableObj;
    tournament.table_id = tableId;
    logger.info("[createInMemoryWaitingTournament] waitingTournaments:", self.waitingTournaments);
    return tournament;
};

/**
 * Create a waiting tournament for a specific type/level and save to database
 * Chỉ được gọi khi có người chơi đăng ký
 */
SngTableService.prototype.createWaitingTournament = function(config, tableType, level, callback) {
    var self = this;
    var tournamentKey = tableType + '_' + level;

    // Check if waiting tournament already exists
    if (self.waitingTournaments[tournamentKey]) {
        return callback(null, self.waitingTournaments[tournamentKey]);
    }

    var tournamentId = uuid.v1();
    var tableId = uuid.v1();

    logger.info("[createWaitingTournament] Creating waiting tournament:", tournamentKey, "tournament_id:", tournamentId);

    // Create tournament in database first
    var dbTournamentData = {
        code: 'SNG_' + tableType + '_' + level + '_' + Date.now(),
        status: 'WAITING',
        player_capacity: config.player_capacity,
        buy_in: config.buy_in,
        fee: config.fee,
        reward_pool: 0, // Will be calculated when tournament starts
        created_at: new Date()
    };

    this.dbService.createSngTournament(dbTournamentData, function(dbErr, dbCode, dbTournament) {
        if (dbErr || !dbTournament) {
            logger.error("[createWaitingTournament] Failed to create tournament in database:", dbErr);
            return callback(new Error('Failed to create tournament in database'));
        }

        logger.info("[createWaitingTournament] Tournament created in database:", dbTournament.id);

        // Create tournament data for memory
        var tournament = {
            id: dbTournament.id, // Use database ID
            table_type: tableType,
            level: level,
            config: config,
            players: [], // Empty initially
            status: 'waiting',
            created_at: Date.now(),
            started_at: null,
            finished_at: null,
            current_blind_level: 0,
            blind_schedule_job: null,
            elimination_log: [],
            registered_count: 0,
            db_tournament: dbTournament // Store database reference
        };

        // Store as waiting tournament
        self.waitingTournaments[tournamentKey] = tournament;

        // Create table object for this waiting tournament
        var tableObj = {
            id: tableId,
            tournament_id: dbTournament.id, // Use database ID
            creator: null, // Will be set when first player joins
            state: 'WAITING',
            tableService: self
        };

        // Create SNG table instance with initial blind level
        var initialBlind = sngTournaments.blind_structure[0];
        tableObj.table = new SngTable(
            initialBlind.small_blind,
            initialBlind.big_blind,
            2, // minPlayers
            config.player_capacity,
            config.initial_chips,
            config.initial_chips,
            'sng', // gameMode
            tableObj
        );

        // Set tournament-specific properties
        tableObj.table.tournament_id = dbTournament.id; // Use database ID
        tableObj.table.blind_structure = sngTournaments.blind_structure;
        tableObj.table.current_blind_level = 0;
        tableObj.table.payout_structure = sngTournaments.reward_distribution;
        tableObj.table.ante = initialBlind.ante || 0;

        // Initialize members array
        tableObj.table.members = [];

        self.tables[tableId] = tableObj;
        tournament.table_id = tableId;

        callback(null, tournament);
    });
};

/**
 * Find or get waiting tournament for registration
 * Nếu chưa có tournament nào được lưu vào database, sẽ tạo mới và lưu vào database
 */
SngTableService.prototype.findOrCreateTournament = function(tableType, level, callback) {
    var self = this;
    var tournamentKey = tableType + '_' + level;

    logger.info("[findOrCreateTournament] Looking for tournament:", tournamentKey);

    // Get current waiting tournament
    var waitingTournament = self.waitingTournaments[tournamentKey];
    logger.info("[findOrCreateTournament] Waiting tournaments:", waitingTournament);
    if (!waitingTournament) {
        // Should not happen if init worked properly, but create one as fallback
        var config = self.findTournamentConfig(tableType, level);
        if (!config) {
            return callback(new Error('Tournament config not found'));
        }

        return self.createWaitingTournament(config, tableType, level, callback);
    }

    // Check if current waiting tournament is full
    if (waitingTournament.registered_count >= waitingTournament.config.player_capacity) {
        // Tournament is full, should have been moved to active already
        // Create a new waiting tournament to replace it
        var config = waitingTournament.config;
        delete self.waitingTournaments[tournamentKey];

        return self.createWaitingTournament(config, tableType, level, callback);
    }

    // Check if this tournament has been saved to database yet
    if (!waitingTournament.db_tournament) {
        // This is an in-memory tournament, need to save to database first
        logger.info("[findOrCreateTournament] Tournament not saved to database yet, creating database record");

        var config = waitingTournament.config;
        var dbTournamentData = {
            table_id: waitingTournament.table_id,
            code: 'SNG_' + tableType + '_' + level + '_' + Date.now(),
            status: 'WAITING',
            player_capacity: config.player_capacity,
            buy_in: config.buy_in,
            fee: config.fee,
            reward_pool: 0, // Will be calculated when tournament starts
            created_at: new Date(),
            metadata: JSON.stringify(waitingTournament)
        };

        self.dbService.createSngTournament(dbTournamentData, function(dbErr, dbCode, dbTournament) {
            if (dbErr || !dbTournament) {
                logger.error("[findOrCreateTournament] Failed to create tournament in database:", dbErr);
                return callback(new Error('Failed to create tournament in database'));
            }

            logger.info("[findOrCreateTournament] Tournament saved to database:", dbTournament.id);

            // Update in-memory tournament with database info
            waitingTournament.id = dbTournament.id;
            waitingTournament.db_tournament = dbTournament;

            // Update table tournament_id
            if (waitingTournament.table_id && self.tables[waitingTournament.table_id]) {
                self.tables[waitingTournament.table_id].tournament_id = dbTournament.id;
                if (self.tables[waitingTournament.table_id].table) {
                    self.tables[waitingTournament.table_id].table.tournament_id = dbTournament.id;
                }
            }

            callback(null, waitingTournament);
        });
    } else {
        // Tournament already saved to database, return it
        callback(null, waitingTournament);
    }
};

/**
 * Register a player for SNG tournament
 * Updated to use database-driven waiting tournaments
 */
SngTableService.prototype.registerTournament = function(uid, tableType, level, callback) {
    var self = this;
    
    logger.info("[registerTournament] uid:", uid, "tableType:", tableType, "level:", level);
    
    // Find or create waiting tournament
    self.findOrCreateTournament(tableType, level, function(err, tournament) {
        if (err) {
            return callback({
                success: false,
                error: 'tournament-not-available'
            });
        }
        
        // Check if player is already registered for this tournament
        var existingPlayer = _.find(tournament.players, function(player) {
            return player.uid === uid;
        });
        
        if (existingPlayer) {
            return callback({
                success: false,
                error: 'already-registered'
            });
        }
        
        var tournamentConfig = tournament.config;
        var requiredPlayers = tournamentConfig.player_capacity;
        
        // Create player registration in database
        var dbPlayerData = {
            tournament_id: tournament.db_tournament.id,
            player_id: uid,
            seat_number: tournament.registered_count, // 0-based seat assignment
            initial_chips: tournamentConfig.initial_chips,
            current_chips: tournamentConfig.initial_chips,
            status: 'ACTIVE',
            joined_at: new Date()
        };

        self.dbService.createSngTournamentPlayer(dbPlayerData, function(dbPlayerErr, dbPlayerCode, dbPlayer) {
            if (dbPlayerErr || !dbPlayer) {
                logger.error("[registerTournament] Failed to create tournament player in database:", dbPlayerErr);
                return callback({
                    success: false,
                    error: 'database-error'
                });
            }

            logger.info("[registerTournament] Tournament player created in database:", dbPlayer.id);

            // Log tournament registration
            self.dbService.createSngTournamentLog({
                tournament_id: tournament.db_tournament.id,
                player_id: uid,
                action_type: 'PLAYER_REGISTERED',
                data: {
                    seat_number: dbPlayerData.seat_number,
                    initial_chips: dbPlayerData.initial_chips,
                    table_type: tableType,
                    level: level
                },
                amount: tournamentConfig.buy_in
            }, function(logErr, logCode, logResult) {
                if (logErr) {
                    logger.error("[registerTournament] Failed to log registration:", logErr);
                }
            });

            // Add player to tournament in memory
            var playerRegistration = {
                uid: uid,
                registered_at: Date.now(),
                table_type: tableType,
                level: level,
                db_player: dbPlayer // Store database reference
            };

            tournament.players.push(playerRegistration);
            tournament.registered_count = tournament.players.length;

            logger.info("[registerTournament] Player added. Current registrations:", tournament.registered_count, "/", requiredPlayers);

            // Check if tournament is now full
            if (tournament.registered_count >= requiredPlayers) {
                // Update tournament reward pool
                var rewardPool = tournament.registered_count * tournamentConfig.buy_in;
                self.dbService.updateSngTournament({
                    tournament_id: tournament.db_tournament.id,
                    status: 'IN_PROGRESS',
                    reward_pool: rewardPool,
                    started_at: new Date()
                }, function(updateErr, updateCode, updateResult) {
                    if (updateErr) {
                        logger.error("[registerTournament] Failed to update tournament status:", updateErr);
                    }
                });

                // Move tournament from waiting to active
                var tournamentKey = tableType + '_' + level;
                self.tournaments[tournament.id] = tournament;
                delete self.waitingTournaments[tournamentKey];

                // Create new in-memory waiting tournament to replace this one
                self.createInMemoryWaitingTournament(tournamentConfig, tableType, level);
                logger.info("Created replacement in-memory waiting tournament for", tableType + '_' + level);

                // Start the full tournament
                self.startTournament(tournament.id, function(startResult) {
                    callback({
                        success: startResult.success,
                        tournament_id: tournament.id,
                        table_id: tournament.table_id,
                        status: 'tournament_started',
                        error: startResult.error
                    });
                });
            } else {
                // Player added to waiting tournament
                callback({
                    success: true,
                    tournament_id: tournament.id,
                    table_id: tournament.table_id,
                    status: 'waiting_for_players',
                    players_needed: requiredPlayers - tournament.registered_count,
                    current_players: tournament.registered_count
                });
            }
        });
    });
};

/**
 * Find tournament configuration by table type and level
 */
SngTableService.prototype.findTournamentConfig = function(tableType, level) {
    var playerCapacity = tableType === '5_PLAYERS' ? 5 :
                        (tableType === '9_PLAYERS' ? 9 : 3); // Added 3_PLAYERS for testing

    return sngTournaments.tournaments.find(function(config) {
        return config.player_capacity === playerCapacity && config.level === level;
    });
};

/**
 * Get count of registered players for a tournament type/level
 */
SngTableService.prototype.getRegisteredPlayersCount = function(tableType, level) {
    var tournamentKey = tableType + '_' + level;
    var waitingTournament = this.waitingTournaments[tournamentKey];

    if (!waitingTournament) {
        return 0;
    }

    return waitingTournament.registered_count || 0;
};

/**
 * Create and start a new SNG tournament
 */
SngTableService.prototype.createTournament = function(players, tableType, level, callback) {
    var self = this;
    
    var tournamentId = uuid.v1();
    var tableId = uuid.v1();
    var tournamentConfig = this.findTournamentConfig(tableType, level);
    
    if (!tournamentConfig) {
        return callback({
            success: false,
            error: 'tournament-config-not-found'
        });
    }
    
    logger.info("[createTournament] Creating tournament:", tournamentId, "with", players.length, "players");
    
    // Create tournament data
    var tournament = {
        id: tournamentId,
        table_type: tableType,
        level: level,
        config: tournamentConfig,
        players: players,
        status: 'starting',
        created_at: Date.now(),
        started_at: null,
        finished_at: null,
        current_blind_level: 0,
        blind_schedule_job: null,
        elimination_log: [] // Track player eliminations
    };
    
    self.tournaments[tournamentId] = tournament;
    
    // Create table object
    var tableObj = {
        id: tableId,
        tournament_id: tournamentId,
        creator: players[0].uid,
        state: 'WAITING',
        tableService: self
    };
    
    // Create SNG table instance with initial blind level
    var initialBlind = sngTournaments.blind_structure[0];
    tableObj.table = new SngTable(
        initialBlind.small_blind,
        initialBlind.big_blind,
        2, // minPlayers
        tournamentConfig.player_capacity,
        tournamentConfig.initial_chips,
        tournamentConfig.initial_chips,
        'sng', // gameMode
        tableObj
    );
    
    // Set tournament-specific properties
    tableObj.table.tournament_id = tournamentId;
    tableObj.table.blind_structure = sngTournaments.blind_structure;
    tableObj.table.current_blind_level = 0;
    tableObj.table.payout_structure = sngTournaments.reward_distribution;
    tableObj.table.ante = initialBlind.ante || 0;
    
    // Initialize members array
    tableObj.table.members = [];
    
    self.tables[tableId] = tableObj;
    
    // Players will be added through registerTournament -> addPlayer flow
    // Start tournament after a short delay to allow all players to join
    setTimeout(function() {
        // Check if all players have joined before starting
        var playersJoined = 0;
        for (var i = 0; i < tableObj.table.maxPlayers; i++) {
            if (tableObj.table.players[i]) {
                playersJoined++;
            }
        }
        
        logger.info("[createTournament] Players joined:", playersJoined, "/", players.length);
        
        if (playersJoined >= players.length) {
            self.startTournament(tournamentId, function(startResult) {
                callback({
                    success: startResult.success,
                    tournament_id: tournamentId,
                    table_id: tableId,
                    error: startResult.error
                });
            });
        } else {
            // Wait a bit more for players to join
            setTimeout(function() {
                self.startTournament(tournamentId, function(startResult) {
                    callback({
                        success: startResult.success,
                        tournament_id: tournamentId,
                        table_id: tableId,
                        error: startResult.error
                    });
                });
            }, 3000); // Additional 3 seconds
        }
    }, 5000); // Default 5 seconds delay since SETTINGS.AUTO_START_COUNTDOWN doesn't exist
};

/**
 * Start a tournament
 */
SngTableService.prototype.startTournament = function(tournamentId, callback) {
    var self = this;
    var tournament = self.tournaments[tournamentId];
    
    if (!tournament) {
        return callback({ success: false, error: 'tournament-not-found' });
    }
    
    var table = null;
    // Find table for this tournament
    for (var tableId in self.tables) {
        if (self.tables[tableId].tournament_id === tournamentId) {
            table = self.tables[tableId];
            break;
        }
    }
    
    if (!table) {
        return callback({ success: false, error: 'table-not-found' });
    }
    
    logger.info("[startTournament] Starting tournament:", tournamentId);

    // Update tournament status
    tournament.status = 'in_progress';
    tournament.started_at = Date.now();
    // table.state = 'IN_PROGRESS';
    
    // Start the poker game
    // table.table.StartGame();
    const tid = table.id;
    logger.info("[startTournament] tid: ", tid, ' -> tournamentId: ', tournamentId);
    var timerDefault = consts.GAME.TIMER.FIRST_START;
    if (this.tables[tid].state !== "JOIN") {
        timerDefault = consts.GAME.TIMER.WAIT_START;
    }
    this.app.rpc.game.sngTableRemote.addJobStartGame(null, tid, timerDefault ,function () {});

    // Schedule blind level increases
    self.scheduleBlindIncrease(tournamentId);
    
    // Broadcast tournament start
    self.broadcastTournamentUpdate(table.id, 'tournament_started');
    
    callback({ success: true });
};

/**
 * Schedule blind level increases every 5 minutes
 */
SngTableService.prototype.scheduleBlindIncrease = function(tournamentId) {
    var self = this;
    var tournament = self.tournaments[tournamentId];
    
    if (!tournament || tournament.status !== 'in_progress') {
        return;
    }
    
    // Use blind_duration_minutes from tournament config, default to 5 minutes
    var blindLevelDuration = (tournament.config.blind_duration_minutes || 5) * 60 * 1000;
    
    tournament.blind_schedule_job = setTimeout(function() {
        self.increaseBlindLevel(tournamentId);
    }, blindLevelDuration);
};

/**
 * Increase blind level for tournament
 */
SngTableService.prototype.increaseBlindLevel = function(tournamentId) {
    var self = this;
    var tournament = self.tournaments[tournamentId];
    
    if (!tournament || tournament.status !== 'in_progress') {
        return;
    }
    
    var currentLevel = tournament.current_blind_level;
    var blindStructure = sngTournaments.blind_structure;
    
    if (currentLevel + 1 < blindStructure.length) {
        tournament.current_blind_level++;
        var newLevel = blindStructure[tournament.current_blind_level];
        
        // Find and update table
        for (var tableId in self.tables) {
            if (self.tables[tableId].tournament_id === tournamentId) {
                var table = self.tables[tableId].table;
                table.smallBlind = newLevel.small_blind;
                table.bigBlind = newLevel.big_blind;
                table.ante = newLevel.ante || 0;
                table.current_blind_level = tournament.current_blind_level;
                
                logger.info("[increaseBlindLevel] Tournament:", tournamentId, "Level:", tournament.current_blind_level + 1, 
                           "Blinds:", newLevel.small_blind, "/", newLevel.big_blind, "Ante:", newLevel.ante || 0);
                
                // Broadcast blind level change
                self.broadcastTournamentUpdate(tableId, 'blind_level_increased', {
                    level: tournament.current_blind_level + 1,
                    small_blind: newLevel.small_blind,
                    big_blind: newLevel.big_blind,
                    ante: newLevel.ante || 0
                });
                
                break;
            }
        }
        
        // Schedule next blind level increase
        self.scheduleBlindIncrease(tournamentId);
    } else {
        logger.info("[increaseBlindLevel] Tournament:", tournamentId, "reached maximum blind level");
    }
};

/**
 * Handle player elimination from tournament
 */
SngTableService.prototype.handlePlayerElimination = function(tournamentId, uid, position) {
    var self = this;
    var tournament = self.tournaments[tournamentId];
    
    if (!tournament) {
        return;
    }
    
    logger.info("[handlePlayerElimination] Tournament:", tournamentId, "Player:", uid, "Position:", position);
    
    // Record elimination
    tournament.elimination_log.push({
        uid: uid,
        position: position,
        eliminated_at: Date.now()
    });
    
    // Check if tournament is finished (only 1 player left)
    var remainingPlayers = tournament.config.player_capacity - tournament.elimination_log.length;
    
    if (remainingPlayers <= 1) {
        self.finishTournament(tournamentId);
    }
};

/**
 * Finish tournament and distribute prizes
 */
SngTableService.prototype.finishTournament = function(tournamentId) {
    var self = this;
    var tournament = self.tournaments[tournamentId];
    
    if (!tournament) {
        return;
    }
    
    logger.info("[finishTournament] Tournament:", tournamentId);
    
    tournament.status = 'finished';
    tournament.finished_at = Date.now();
    
    // Cancel blind schedule
    if (tournament.blind_schedule_job) {
        clearTimeout(tournament.blind_schedule_job);
    }
    
    // Calculate and distribute prizes
    self.distributePrizes(tournamentId);
    
    // Clean up table
    for (var tableId in self.tables) {
        if (self.tables[tableId].tournament_id === tournamentId) {
            self.tables[tableId].state = 'FINISHED';
            break;
        }
    }
};

/**
 * Distribute prizes based on tournament results
 */
SngTableService.prototype.distributePrizes = function(tournamentId) {
    var self = this;
    var tournament = self.tournaments[tournamentId];
    
    if (!tournament) {
        return;
    }
    
    var prizePool = tournament.config.prize_pool;
    var payoutStructure = tournament.config.payout_structure;
    
    // Sort elimination log by position (1st, 2nd, 3rd)
    var sortedResults = tournament.elimination_log.slice().sort(function(a, b) {
        return a.position - b.position;
    });
    
    // Find winner (the player not in elimination log)
    var allPlayerIds = tournament.players.map(function(p) { return p.uid; });
    var eliminatedPlayerIds = sortedResults.map(function(e) { return e.uid; });
    var winnerId = allPlayerIds.find(function(id) { return eliminatedPlayerIds.indexOf(id) === -1; });
    
    if (winnerId) {
        // Add winner to results
        sortedResults.unshift({
            uid: winnerId,
            position: 1,
            eliminated_at: tournament.finished_at
        });
    }
    
    // Distribute prizes
    sortedResults.forEach(function(result, index) {
        if (index < payoutStructure.length) {
            var payout = payoutStructure[index];
            var prizeAmount = Math.floor(prizePool * payout.percentage);
            
            // Update player balance
            self.app.rpc.db.dbRemote.updatePlayerBalance('*', result.uid, prizeAmount, 
                'SNG Tournament Prize - Position ' + payout.position, function(updateErr) {
                if (!updateErr) {
                    logger.info("[distributePrizes] Awarded", prizeAmount, "to player", result.uid, 
                               "for position", payout.position);
                }
            });
        }
    });
    
    // Log tournament completion
    self.logTournamentCompletion(tournament, sortedResults);
};

/**
 * Log tournament completion to database
 */
SngTableService.prototype.logTournamentCompletion = function(tournament, results) {
    // Implementation depends on your logging requirements
    logger.info("[logTournamentCompletion] Tournament completed:", {
        tournament_id: tournament.id,
        table_type: tournament.table_type,
        level: tournament.level,
        results: results,
        duration: tournament.finished_at - tournament.started_at
    });
};

/**
 * Leave tournament (unregister before start)
 */
SngTableService.prototype.leaveTournament = function(uid, tournamentId, callback) {
    var self = this;
    
    // If tournament ID is provided and exists in active tournaments, handle differently
    if (tournamentId && self.tournaments[tournamentId]) {
        return callback({
            success: false,
            error: 'tournament-already-started'
        });
    }
    
    // Find and remove from waiting tournaments
    var found = false;
    var totalFee = 0;
    
    // Check all waiting tournaments
    for (var tournamentKey in self.waitingTournaments) {
        var tournament = self.waitingTournaments[tournamentKey];
        
        // If specific tournament ID provided, only check that tournament
        if (tournamentId && tournament.id !== tournamentId) {
            continue;
        }
        
        var playerIndex = _.findIndex(tournament.players, function(player) {
            return player.uid === uid;
        });
        
        if (playerIndex !== -1) {
            // Remove player from tournament
            tournament.players.splice(playerIndex, 1);
            tournament.registered_count = tournament.players.length;
            
            totalFee = tournament.config ? (tournament.config.buy_in + tournament.config.fee) : 0;
            found = true;
            
            logger.info("[leaveTournament] Removed player", uid, "from tournament", tournament.id, 
                       "New count:", tournament.registered_count);
            break;
        }
    }
    
    if (found) {
        callback({
            success: true,
            total_fee: totalFee
        });
    } else {
        callback({
            success: false,
            error: 'registration-not-found'
        });
    }
};

/**
 * Get table by ID
 */
SngTableService.prototype.getTable = function(tid) {
    return this.tables[tid];
};

/**
 * Add member to table (similar to regular table service)
 */
SngTableService.prototype.addMember = function(tid, uid, cb) {
    logger.info("[addMember] >> tid: ", tid, ' -> uid: ', uid);

    var me = this;
    var channelService = me.app.get('channelService');
    var table = this.tables[tid];
    if(!table){
        cb('table-not-found');
        return;
    }

    /**
     * Kiểm tra xem uid mới này có đang ngồi chầu rìa hay đang ngồi bàn hay đang chơi không
     * - Nếu có thì remove uid này trước
     */
    me.app.rpc.manager.userRemote.getUserCacheByUid(null, uid, function (user) {
    //UserStore.getByAttr('id', uid, false, function(e, user){
    //userDao.getUserById(uid, function(e, user) {
        logger.info("user trong cached with uid ", uid ,": data:", user);
        const sid = user?.serverId ?? null;
        if (user == null){
            cb("user-not-exist-in-game");
            return false;
        }

        user = user.player;
        logger.info("-> app|| services || tablesServices || addMember");
        logger.info("----> users: " + JSON.stringify(user));

        /**
         * check playersToAdd , if = 0 then reset gameWinners = []
         */
        if (table.table.playersToAdd.length == 0) {
            table.table.gameWinners = [];
            logger.info("TH trong ban choi khong ai ngoi => reset gameWinner");
        }

        /**
         * nếu trong bàn không có người nào thì initGame mới
         */
        if (table.table.members.length == 0) {
            //logger.info("Trong Ban Khong co ai dang dung hay ngoi => table info: ", table);
            table.table.initNewGame();
        }

        if(!user){
            cb(e);
        }
        // var sid = await getSidByUid(uid, me.app);
        // const sid = user?.serverId ?? null;
        if(!sid){
            return cb('invalid-connector-server');
        }
        // TODO: reduce payload by handling based on game state
        var channel = channelService.getChannel(tid, true);
        channel.add(uid, sid);

        /**
         * - khi có người chơi vào bàn
         * + TH JOIN: bàn chưa chơi => trả về danh sách nguồi chơi đang ngồi trong bàn
         * + TH IN_PROGRESS: bàn đang chơi => trả về danh sách người đang chơi + người đang ngồi chờ
         */
        var listMembers = me.getPlayersJSON(tid, 'playersToAdd', uid);
        var players         = me.getPlayersJSON(tid, 'players', uid);
        let playersToAdd    = [];
        if (table.state === "IN_PROGRESS") {
            // var players         = me.getPlayersJSON(tid, 'players', uid);
            playersToAdd = me.getPlayersJSON(tid, 'playersToAdd', uid);
            listMembers = _.union(players, playersToAdd);
        }
        logger.info("MINH>>JOIN_TABLE>>TableJSON: ", me.getTableJSON(tid, uid));
        channelService.pushMessageByUids({
            route  : consts.GAME.ROUTER.JOIN_TABLE, //'JoinTable',
            members : listMembers, // Already clean from getPlayersJSON
            players: players, // Already clean from getPlayersJSON
            waitings: playersToAdd, // Already clean from getPlayersJSON
            board: me.getTableJSON(tid, uid)?.board ?? [],
            currentPlayerTime: me.getTableJSON(tid, uid)?.currentPlayerTime ?? 0,
            currentPlayerIndex: me.getTableJSON(tid, uid)?.currentPlayer ?? null,
            timestamp: Math.floor(Date.now() / 1000) // tạm để
        }, [{
            uid : uid,
            sid : channel.getMember(uid)['sid'] //sid
        }], function(e) {
            if(e){
                logger.error('JoinTable: unable to push members ', e);
            }

            logger.debug('initiated player '+uid+' into table '+tid+' with state '+table.state);

            var checkUser = checkUserInTable(user, table);
            if (checkUser == false){
                logger.info("user: ", user, " added into table: ", tid);
                table.table.members.push(user);
            }

            channel.pushMessage({
                route   : consts.GAME.ROUTER.UPDATE_USERS, //'onUpdateUsers',
                members : table.table.members.map(function(member) {
                    return {
                        id: member.id,
                        playerName: member.playerName || member.nick_name,
                        username: member.username || member.nick_name,
                        avatar: member.avatar,
                        level: member.level,
                        vippoint: member.vippoint,
                        exp: member.exp,
                        balance: member.balance,
                        chips: member.chips,
                        type: member.type,
                        isState: member.isState
                    };
                })
            });

            // update gameId to userCached
            // me.app.rpc.manager.userRemote.onUserJoin(null, uid, null, tid, function () {
            me.app.rpc.manager.userRemote.onUserJoin(null, uid, tid, null, function () {
                logger.info("[tableService -> addMember -> update ", uid ," play with gameId: ", tid);
            });

            // clearJobQueue
            // -------------------------------------------------------------------------------------
            me.app.rpc.manager.userRemote.clearJobQueue(null, uid, function () {});

            // cb(); // removed
            // tra ve danh sach những người chơi đã ngồi xuống bàn
            cb(me.getPlayersJSON(tid, 'playersToAdd', uid));

        });

    });
};

/**
 * Remove member from table
 */
SngTableService.prototype.removeMember = function(tid, uid, callback) {
    var table = this.tables[tid];
    if (!table) {
        return callback('table-not-found');
    }
    
    var memberIndex = table.table.members.indexOf(uid);
    if (memberIndex !== -1) {
        table.table.members.splice(memberIndex, 1);
    }
    
    callback(null);
};

/**
 * Add player to SNG tournament table - Clone from tableService.js
 * Adapted for SNG tournament logic without buy-in money handling
 */
SngTableService.prototype.addPlayerToSngTable = function(tid, uid, initialChips, index, cb) {
    logger.info("[sngTableService.addPlayerToSngTable] uid ", uid, " in actorNr: ", index, " in tid ", tid, " initialChips: ", initialChips);
    var me = this;
    var _cbRes = {};
    
    if(!this.tables[tid]) {
        _cbRes = {
            msg: "table-not-found",
            code: CODE.TABLE.TABLE_NOT_FOUND
        };
        return cb(_cbRes);
    }

    var table = this.tables[tid].table;

    // Auto-assign seat index if not provided
    if (typeof index === "undefined" || index === null) {
        // Find first available seat
        index = null;
        for (var i = 0; i < table.maxPlayers; i++) {
            if (!table.playersToAdd.find(function(p) { return p.actorNr === i; }) &&
                !table.players.find(function(p) { return p.actorNr === i; })) {
                index = i;
                break;
            }
        }
        
        if (index === null) {
            logger.error("No available seats in SNG table ", tid);
            _cbRes = {
                msg: "no-available-seats", 
                code: CODE.TABLE.TABLE_FULL
            };
            return cb(_cbRes);
        }
    }

    logger.info("=> SNG Table isSitDown: ", table.isSitDown);

    // Set table as processing
    table.isSitDown = true;

    // Check if player already joined
    if(me.getPlayerIndex(tid, uid, 'playersToAdd')) {
        logger.info("addPlayerToSngTable >> uid ",uid," already-joined");
        table.isSitDown = false;
        _cbRes = {
            msg: "already-joined",
            code: CODE.TABLE.ALREADY_JOINED
        };
        return cb(_cbRes);
    }

    initialChips = parseInt(initialChips);
    initialChips = Math.round(initialChips);

    // Check if seat is available
    var playerToAddCheck = _.find(this.tables[tid].table['playersToAdd'], function(item) { return item.actorNr == index });
    var playerCheck = _.find(this.tables[tid].table['players'], function(item) { return item.actorNr == index });

    logger.info("[sngTableService] playerToAddCheck: ", playerToAddCheck);
    logger.info("[sngTableService] playerCheck: ", playerCheck);

    if (typeof playerCheck !== "undefined" || typeof playerToAddCheck !== "undefined") {
        logger.info("[sngTableService] position (", index, ") not Available");
        table.isSitDown = false;
        _cbRes = {
            msg: "invalid-actorNr",
            code: CODE.TABLE.INVALID_ACTORNR
        };
        return cb(_cbRes);
    }

    // Check if uid already in playersToAdd
    var checkUid = _.findWhere(table.playersToAdd, {id: uid});
    if (checkUid){
        logger.info("[sngTableService] uid already-joined---");
        table.isSitDown = false;
        _cbRes = {
            msg: "already-joined",
            code: CODE.TABLE.ALREADY_JOINED
        };
        return cb(_cbRes);
    }

    // Check if uid already playing
    var checkUidPlay = _.findWhere(table.players, {id: uid});
    if(checkUidPlay){
        logger.info("[sngTableService] user-is-playing---");
        table.isSitDown = false;
        _cbRes = {
            msg: "user-is-playing", 
            code: CODE.TABLE.USER_IS_PLAYING
        };
        return cb(_cbRes);
    }

    logger.info("SNG TABLE playersToAdd:", table.playersToAdd);

    // Get user info from cache
    me.app.rpc.manager.userRemote.getUserCacheByUid(null, uid, function (user) {
        logger.info("[sngTableService] addPlayerToSngTable->user: ", user);
        
        if (!user) {
            logger.info("SNG user not found in cache: ", user);
            table.isSitDown = false;
            _cbRes = {
                msg: "user-not-found",
                code: CODE.TABLE.USER_NOT_FOUND
            };
            return cb(_cbRes);
        }

        // Check if user is playing in other table
        logger.info("SNG addPlayer: user.isPlayer = ", user.isPlayer, " and tid ", tid);
        if (user.isPlayer === true){
            logger.info("SNG addPlayer: user.uid ", user.player.id, " playing in table ", user.gameId);
            table.isSitDown = false;
            _cbRes = {
                msg: "playing-in-other-table",
                code: CODE.TABLE.PLAYING_IN_OTHER_TABLE
            };
            return cb(_cbRes);
        }

        user = user.player;
        logger.info("SNG addPlayer >> user.balance: ", user.balance, ' >> initialChips: ', initialChips);

        // For SNG, we don't deduct money from balance, just use initial chips
        var chips = user.balance; // Keep original balance
        logger.info("[sngTableService.addPlayerToSngTable] chips: ", chips, ' -> initialChips: ', initialChips, ' -> user.balance: ', user.balance);
        
        // For SNG tournaments, we don't update user balance/money, just set player state
        var _userUpdate = {uid: user.id, balance: chips, chips: initialChips, actorNr: index, gameId: tid, isPlayer:true};

        logger.info("[sngTableService] _userUpdate: ", _userUpdate);
        me.app.rpc.manager.userRemote.onUpdateMoneyGameByUid(null, _userUpdate, function (e, updatedUser) {
            logger.info("[sngTableService][onUpdateMoneyGameByUid] updatedUser: ", updatedUser, ' -> e: ', e);
            if(e) {
                table.isSitDown = false;
                cb(e);
                return;
            }
            updatedUser = updatedUser.player;

            table.eventEmitter.emit('playerJoined');

            // Update member chips if exists
            var mIndex = me.getPlayerIndex(tid, updatedUser.id, 'members');
            if(typeof mIndex === 'number'){
                table.members[mIndex].chips = chips;
            }

            logger.info("---> SNG mIndex: " + mIndex  + " => vi tri:" + index);
            logger.info("---> SNG addPlayer ", user);

            // Add player to SNG table
            table.AddPlayer(index, updatedUser.nick_name, initialChips, uid, user.avatar, user.level, user.vippoint, user.exp, user.type);

            logger.info("---> SNG After AddPlayer - table.playersToAdd: ", table.playersToAdd);
            logger.info("---> SNG After AddPlayer - table.players: ", table.players);
            logger.info("---> SNG After AddPlayer - table.members: ", table.members);

            // Push player info
            me.pushPlayerInfo(tid, user.id, function (e) {});

            // Update users in channel
            var channelService = me.app.get('channelService');
            var channel = channelService.getChannel(tid, true); // true = ensure channel exists

            logger.info("---> SNG Channel members: ", channel ? Object.keys(channel.records || {}) : 'no channel');
            
            // Force ensure current player is in channel
            if (channel && typeof channel.getMember(uid) === "undefined") {
                var sessionService = me.app.get('sessionService');
                var sessions = sessionService.getByUid(uid);
                if (sessions && sessions.length > 0) {
                    var session = sessions[0];
                    channel.add(uid, session.frontendId);
                    logger.info("---> SNG Force added", uid, "to channel", tid);
                }
            }
            
            // Update users in channel
            if (channel) {
                // Create clean members array without circular references
                var cleanMembers = table.members.map(function(member) {
                    return {
                        id: member.id,
                        playerName: member.playerName || member.nick_name,
                        username: member.username || member.nick_name,
                        avatar: member.avatar,
                        level: member.level,
                        vippoint: member.vippoint,
                        exp: member.exp,
                        balance: member.balance,
                        chips: member.chips,
                        type: member.type,
                        isState: member.isState
                    };
                });
                
                channel.pushMessage({
                    route   : consts.GAME.ROUTER.UPDATE_USERS,
                    members : cleanMembers
                });
            }

            var timerDefault = consts.GAME.TIMER.FIRST_START;
            if (me.tables[tid].state !== "JOIN") {
                timerDefault = consts.GAME.TIMER.WAIT_START;
            }

            logger.info("---> SNG Table State: ", me.tables[tid].state);

            var listJoinGame    = me.getPlayersJSON(tid, 'playersToAdd', uid);
            var listMembers     = me.getPlayersJSON(tid, 'playersToAdd', uid);
            var listReceiver    = []; // lưu danh sách người nhận message (uid, sid) => TH JOIN
            listReceiver        = me.getReceiver(listJoinGame, channel);
            var waitingsPlayer  = []; // TH bàn chưa chơi thì = rỗng

            var listNotEnough       = me.getPlayersJSON(tid, 'playersToNotEnough', uid);
            var notEnoughReceiver   = me.getReceiver(listNotEnough, channel);

            // Đảm bảo current player luôn có trong listReceiver
            if (typeof channel.getMember(uid) !== "undefined") {
                var currentPlayerReceiver = {uid: uid, sid: channel.getMember(uid)['sid']};
                // Kiểm tra xem current player đã có trong listReceiver chưa
                var existingReceiver = listReceiver.find(function(r) { return r.uid === uid; });
                if (!existingReceiver) {
                    listReceiver.push(currentPlayerReceiver);
                }
            }

            logger.info("SNG listJoinGame: ", listJoinGame, " => uid: ", uid);

            let listPlayers = [];
            if (me.tables[tid].state === "IN_PROGRESS" || me.tables[tid].state === "END_GAME") {
                listPlayers = me.getPlayersJSON(tid, 'players', uid);
                listMembers = _.union(listMembers, listPlayers);

                if (me.tables[tid].state === "IN_PROGRESS")
                    waitingsPlayer = me.getPlayersJSON(tid, 'playersToAdd', uid);

                if (me.tables[tid].state === "END_GAME")
                    waitingsPlayer = [];
            }

            listMembers = _.uniq(listMembers);
            listReceiver = _.uniq(listReceiver);

            logger.info("SNG listReceiver: ", listReceiver);
            logger.info("SNG waitingsPlayer: ", waitingsPlayer);

            // Broadcast JOIN_GAME event - chỉ khi có receiver
            if (listReceiver.length > 0) {
                channelService.pushMessageByUids({
                    route  : consts.GAME.ROUTER.JOIN_GAME,
                    members : listMembers,
                    players : me.getPlayersJSON(tid, 'players', uid),
                    waitings: waitingsPlayer,
                    state: me.tables[tid].state,
                    board: me.getTableJSON(tid, uid)?.board ?? [],
                    currentPlayerTime: me.getTableJSON(tid, uid)?.currentPlayerTime ?? 0,
                    currentPlayerIndex: me.getTableJSON(tid, uid)?.currentPlayer ?? null,
                    tournament_mode: true,
                    tournament_id: me.tables[tid].tournament_id,
                    timestamp: Math.floor(Date.now() / 1000)
                }, listReceiver, function(e) {
                    if(e) {
                        logger.error(consts.GAME.ROUTER.JOIN_GAME, ': unable to push members ', e);
                    }

                    logger.info("SNG table: " , table);
                    logger.info("SNG table.members: " , table.members);
                    logger.info("SNG listMembers: " , listMembers);
                    
                    // Create clean array of member IDs to avoid circular references in getChauRia
                    var cleanTableMembers = table.members.map(function(member) {
                        return { id: member.id };
                    });
                    
                    var chauria = me.getChauRia(cleanTableMembers, listMembers, channel);

                    var listAll = _.union(chauria, notEnoughReceiver);
                    chauria = _.filter(listAll, function(obj){ return !_.findWhere(notEnoughReceiver, obj); });

                    logger.info("SNG uid: " , uid, " -> chauria:", chauria);

                    if (chauria.length > 0) {
                        logger.info("-> SNG chauria(", chauria.length ,") > 0 -> pushMessage");
                        
                        // Get clean player JSON for chauria message
                        var cleanPlayerMsg = me.getPlayerJSON(tid, uid, 'playersToAdd') || me.getPlayerJSON(tid, uid);
                        
                        channelService.pushMessageByUids({
                            route: consts.GAME.ROUTER.JOIN_GAME,
                            members: listMembers,
                            players: listPlayers,
                            msg: cleanPlayerMsg,
                            waitings: waitingsPlayer,
                            state: me.tables[tid].state,
                            board: me.getTableJSON(tid, uid)?.board ?? [],
                            timestamp: Math.floor(Date.now() / 1000),
                            currentPlayerTime: me.getTableJSON(tid, uid)?.currentPlayerTime ?? 0,
                            currentPlayerIndex: me.getTableJSON(tid, uid)?.currentPlayer ?? null,
                            tournament_mode: true,
                            tournament_id: me.tables[tid].tournament_id,
                        }, chauria, function (e) {
                            logger.info("-> SNG callback sau khi pushMessage cho chau ria");
                        });
                    }
                });
            } else {
                logger.warn("SNG: No receivers available for JOIN_GAME event, skipping broadcast");
            }

            // For SNG, don't auto start game yet - wait for tournament ready signal
            
            // Reset sit down state
            table.isSitDown = false;
            logger.info("[end] SNG addPlayer uid ", uid, " đã ngồi xong with actorNr:", index, " and isSitDown: ", table.isSitDown);
            cb();
        });
    });
};

/**
 * Hàm kiểm tra user đã có thông tin trong bàn hay chưa
 * @param user
 * @param table
 * @return {boolean}
 */
var checkUserInTable = function (user, table) {
    for(var i=0;i<table.table.members.length;i+=1){
        if (table.table.members[i].id == user.id) {
            return true; // đã có thông tin trong bàn rồi
        }
    }
    return false; // chưa có thông tin => được quyền add vào
};

SngTableService.prototype.addPlayer = function(tid, uid, buyIn, index, cb){
    logger.info("[tableService.addPlayer] uid ", uid, " in actorNr: ", index, " in tid ", tid, " buyIn: ", buyIn);
    var me = this;
    var _cbRes = {};
    if(!this.tables[tid]) {
        _cbRes = {
            msg: "table-not-found",
            code: CODE.TABLE.TABLE_NOT_FOUND
        };
        //return cb('table-not-found');
        return cb(_cbRes);
    }

    var table = this.tables[tid].table;

    if (typeof index === "undefined") {
        logger.info("Invaild actorNr ", index);
        _cbRes = {
            msg: "invalid-actorNr",
            code: CODE.TABLE.INVALID_ACTORNR
        };
        //return cb("invalid-actorNr");
        return cb(_cbRes);
    }

    logger.info("=> Trang thai isSitDown: ", table.isSitDown);

    // 27.02.2025 - bỏ chặn trạng thái isSitDown
    // if (table.isSitDown) {
    //     logger.info("Đang xử lý ngồi cho người khác uid ", uid, " với actorNr: ", index, " đợi nhé");
    //     _cbRes = {
    //         msg: "action-sitdown-is-active",
    //         code: CODE.TABLE.ACTION_SITDOWN_ACTIVE
    //     };
    //     //return cb("action-sitdown-is-active");
    //     return cb(_cbRes);
    // }
    // bắt đầu set chế độ đang hoạt động
    // -----------------------------------------------------------------------------------------------------------
    table.isSitDown = true;

    if(me.getPlayerIndex(tid, uid, 'playersToAdd')) {
        logger.info("addPlayer >> getPlayerIndex with uid ",uid," already-joined ---");
        table.isSitDown = false;
        _cbRes = {
            msg: "already-joined",
            code: CODE.TABLE.ALREADY_JOINED
        };
        //return cb('already-joined');
        return cb(_cbRes);
    }
    // logger.info("addPlayer >> getPlayerIndex before buyIn ",buyIn);
    buyIn = parseInt(buyIn);

    // Đưa phần này xuống dưới để check điều kiện người chơi đã chơi ván trước thì cho phép buyin số tiền thấp hơn min buyin của bàn
    // logger.info("addPlayer >> getPlayerIndex >> buyIn: ",buyIn, ' -> table.minBuyIn: ', table.minBuyIn, ' -> table.maxBuyIn: ', table.maxBuyIn);
    // if(isNaN(buyIn) || buyIn < table.minBuyIn || buyIn > table.maxBuyIn) {
    //     let isExistingInPreviousPlayer = _.some(table.previousPlayers, player => player.id === user.id);
    //     logger.info("[addPlayer]-> isExistingInPreviousPlayer: ", isExistingInPreviousPlayer);

    //     table.isSitDown = false;
    //     _cbRes = {
    //         msg: "invalid-buyin",
    //         code: CODE.TABLE.INVALID_BUYIN
    //     };
    //     return cb(_cbRes);
    // }

    buyIn = Math.round(buyIn);

    // Kiếm tra xem vị trí ghế ngồi còn trống không
    // Nếu trả về true => vị trí đã có người ngồi
    // Nếu trả về false => được ngồi
    var isState, playerToAddCheck, playerCheck;

    playerToAddCheck    = _.find(this.tables[tid].table['playersToAdd'], function(item) { return item.actorNr == index });
    playerCheck         = _.find(this.tables[tid].table['players'], function(item) { return item.actorNr == index });

    logger.info("app||services||tableService||addPlayer->playerToAddCheck: ", playerToAddCheck);
    logger.info("app||services||tableService||addPlayer->playerCheck: ", playerCheck);

    if (typeof playerCheck !== "undefined" || typeof playerToAddCheck !== "undefined") {
        logger.info("app||services||tableService|| position (", index, ") not Available");
        table.isSitDown = false;
        // return cb('actorNr-notAvailable');
        _cbRes = {
            msg: "invalid-actorNr",
            code: CODE.TABLE.INVALID_ACTORNR
        };
        return cb(_cbRes);
    }

    /**
     * Kiểm tra uid có nằm trong danh sách đang ngồi hay không
     * Nếu có rồi thì return luôn
     * Nếu chưa có thì cho qua :D
     */
    //var checkUid = checkUserInToPlayersToAdd(table, uid);
    var checkUid = _.findWhere(table.playersToAdd, {id: uid});
    if (checkUid){
        logger.info("app||services||tableService||addPlayer2->uid already-joined---");
        table.isSitDown = false;
        //return cb('user-is-already-joined');
        _cbRes = {
            msg: "already-joined",
            code: CODE.TABLE.ALREADY_JOINED
        };
        return cb(_cbRes);
    }

    /**
     * Check xem uid này có đang chơi game hay không, nếu đang chơi mà đứng dậy, thì ko cho ngồi lại
     */
    //var checkUidPlay = me.checkUserInToPlayers(table, uid);
    var checkUidPlay = _.findWhere(table.players, {id: uid});
    if(checkUidPlay){
        logger.info("app||services||tableService||addPlayer->user-is-playing---");
        table.isSitDown = false;
        //return cb('user-is-playing');
        _cbRes = {
            msg: "user-is-playing",
            code: CODE.TABLE.USER_IS_PLAYING
        };
        return cb(_cbRes);
    }

    logger.info("IN DANH SACH ADD_TO_PLAY:", table.playersToAdd);

    //UserStore.getByAttr('id', uid, false, function(e, user){
    me.app.rpc.manager.userRemote.getUserCacheByUid(null, uid, function (user) {
    // userDao.getPlayer(uid, function (user) {
        logger.info("app||services||tableService||addPlayer->user: ", user);
        // check user co ton tai hay khong
        if (!user) {
            logger.info("user lay tu userCached : ", user);
            table.isSitDown = false;
            // cb('user-not-found');
            // return;
            _cbRes = {
                msg: "user-not-found",
                code: CODE.TABLE.USER_NOT_FOUND
            };
            return cb(_cbRes);
        }

        // Kiểm tra xem người chơi có chơi ở ván trước đó hay không theo tham số previousPlayers
        const previousPlayers = me.getPlayersJSON(tid, 'previousPlayers', uid);
        const isExistingInPreviousPlayer = _.some(previousPlayers, player => player.id === user.uid);
        // const isExistingInPreviousPlayer2 = _.some(previousPlayers, player => player.id === user.uid);
        logger.info("[addPlayer] >> isExistingInPreviousPlayer: ", isExistingInPreviousPlayer, ' -> user.uid: ', user.uid);
        logger.info("[addPlayer] >> previousPlayers11111: ", previousPlayers);
        // logger.info("[addPlayer] >> table.previousPlayers: ", table.previousPlayers);
        logger.info("[addPlayer] >> getPlayerIndex >> buyIn: ",buyIn, ' -> table.minBuyIn: ', table.minBuyIn, ' -> table.maxBuyIn: ', table.maxBuyIn);
        if(isNaN(buyIn) || (buyIn < table.minBuyIn || buyIn > table.maxBuyIn)) {
            if (!isExistingInPreviousPlayer) {
                table.isSitDown = false;
                _cbRes = {
                    msg: "invalid-buyin",
                    code: CODE.TABLE.INVALID_BUYIN
                };
                return cb(_cbRes);
            }
        }

        // check xem uid co dang choi game o ban khac hay khong
        logger.info("addPlayer: user.isPlayer = ", user.isPlayer, " and tid ", tid);
        //if (user.gameId !== null){
        if (user.isPlayer === true){
            logger.info("addPlayer: user.uid ", user.player.id, " playing in table ", user.gameId);
            table.isSitDown = false;
            // cb('playing-in-other-table');
            // return;
            _cbRes = {
                msg: "playing-in-other-table",
                code: CODE.TABLE.PLAYING_IN_OTHER_TABLE
            };
            return cb(_cbRes);
        }

        user = user.player;
        logger.info("addPlayer >> user.balance: ", user.balance, ' >> table.minBuyIn: ', table.minBuyIn, ' >> buyIn: ', buyIn);
        //if(Math.round(user.chips) < table.minBuyIn){
        if(Math.round(user.balance) < table.minBuyIn && !isExistingInPreviousPlayer) {
            table.isSitDown = false;
            // cb('below-minimum-buyin');
            // return;
            _cbRes = {
                msg: "below-minimum-buyin",
                code: CODE.TABLE.BELOW_MININUM_BUYIN
            };
            return cb(_cbRes);
        }

        // logger.info("[addPlayer]-> table>>players: ", me.tables[tid].table['players']);
        // logger.info("[addPlayer]-> table>>players2: ", me.getPlayersJSON(tid, 'players', uid));
        logger.info("[addPlayer]-> table>>previousPlayers: ", me.getPlayersJSON(tid, 'previousPlayers', uid));
        logger.info("[addPlayer]-> table>>playersToNotEnough: ", me.getPlayersJSON(tid, 'playersToNotEnough', uid));
        //if(Math.round(user.chips) < buyIn){
        if(Math.round(user.balance) < buyIn){
            // check xem người chơi có chơi ở ván trước đó hay không theo tham số previousPlayers
            // const isExistingPlayer = table.table.members.some(member => member.id === user.id);
            // const previousPlayers = me.getPlayersJSON(tid, 'previousPlayers', uid);
            logger.info("[addPlayer]-> previousPlayers: ", previousPlayers);
            // const isExistingInPreviousPlayer = _.some(table.previousPlayers, player => player.id === user.id);
            // logger.info("[addPlayer]-> isExistingInPreviousPlayer: ", isExistingInPreviousPlayer);

            if (!isExistingInPreviousPlayer) {
                table.isSitDown = false;
                _cbRes = {
                    msg: "not-enough-chips",
                    code: CODE.USER.NOT_ENOUGH_MONEY
                };
                return cb(_cbRes);
            }
            // If player is existing member, allow them to continue with available balance
            buyIn = Math.round(user.balance);
        }
        //var chips = Math.round(user.chips - buyIn);
        var chips = Math.round(user.balance - buyIn);
        logger.info("[tableService.addPlayer] chips: ", chips, ' -> buyIn: ', buyIn, ' -> user.balance: ', user.balance);
        //var _userUpdate = {uid: user.id, balance: chips, chips: buyIn, actorNr: index};
        // var _userUpdate = {uid: user.id, balance: chips, chips: buyIn, actorNr: index, gameId: tid, isPlayer:true};
        
        // Create clean user object without circular references for RPC call
        var _userUpdate = {
            uid: user.id, 
            balance: chips, 
            chips: buyIn, 
            actorNr: index, 
            gameId: tid, 
            isPlayer: true
        };

        logger.info("[addPlayer]->_userUpdate: ", _userUpdate);
        me.app.rpc.manager.userRemote.onUpdateMoneyGameByUid(null, _userUpdate, function (e, updatedUser) {
            logger.info("[addPlayer][onUpdateMoneyGameByUid]->updatedUser: ", updatedUser, ' -> e: ', e);
            if(e) {
                table.isSitDown = false;
                cb(e);
                return;
            }
            updatedUser = updatedUser.player;

            table.eventEmitter.emit('playerJoined');

            // cập nhật lại index - nhận dữ liệu vị trí ghé ngồi từ client gửi lên
            var mIndex = me.getPlayerIndex(tid, updatedUser.id, 'members');

            if(typeof mIndex === 'number'){
                table.members[mIndex].chips = chips;
            }

            // added logs
            logger.info("---> mIndex: " + mIndex  + " => vi tri:" + index);
            logger.info("---> addPlayer ", user);

            table.AddPlayer(index, updatedUser.nick_name, buyIn, uid, user.avatar, user.level, user.vippoint, user.exp, user.type);

            // remove
            me.pushPlayerInfo(tid, user.id, function (e) {});
            logger.info("[addPlayer] table: ", table.members);
            me.app.get('channelService').getChannel(tid, true).pushMessage({
                route   : consts.GAME.ROUTER.UPDATE_USERS, //'onUpdateUsers',
                members : table.members.map(function(member) {
                    return {
                        id: member.id,
                        playerName: member.display_name || member.full_name,
                        username: member.username || member.nick_name,
                        avatar: member.avatar,
                        level: member.level,
                        vippoint: member.vippoint,
                        exp: member.exp,
                        balance: member.balance,
                        chips: member.chips,
                        type: member.type,
                        isState: member.isState
                    };
                })
            });

            /**
             *  Sau khi thêm người chơi thành công vào bàn, nếu bàn chưa chơi hay vừa kết thúc thì reset lại jobId
             *  Nếu bàn đang chơi thì bỏ qua
             */
            // -----------------------------------------------------------------------------------------------------

            // if (me.tables[tid].state === "JOIN" || me.tables[tid].state === "END_GAME") {
            //     me.app.rpc.game.sngTableRemote.clearJobStartGame(null, tid, function () {});
            // }

            var timerDefault = consts.GAME.TIMER.FIRST_START;
            if (me.tables[tid].state !== "JOIN") {
                timerDefault = consts.GAME.TIMER.WAIT_START;
            }

            /**
             * Khi có người chơi từ danh sách chầu rìa -> ngồi xuống
             * Push về cho chính người chơi ngồi xuống -> push về danh sách chầu rìa thông tin mới
             */
            var channelService = me.app.get('channelService');
            var channel = channelService.getChannel(tid, true);

            /**
             * Trường hợp mặc định là state = JOIN
             * - Người nhận messages gồm:
             * + Bản thân người ngồi
             * + Danh sách người chơi đang ngồi
             * + Danh sách chầu rìa
             */
            // Danh sách người chơi đang ngồi trong bàn
            var listJoinGame    = me.getPlayersJSON(tid, 'playersToAdd', uid);
            var listMembers     = me.getPlayersJSON(tid, 'playersToAdd', uid);
            var listReceiver    = []; // lưu danh sách người nhận message (uid, sid) => TH JOIN
            listReceiver        = me.getReceiver(listJoinGame, channel);
            var waitingsPlayer  = []; // TH bàn chưa chơi thì = rỗng

            var listNotEnough       = me.getPlayersJSON(tid, 'playersToNotEnough', uid);
            var notEnoughReceiver   = me.getReceiver(listNotEnough, channel);

            //listReceiver = _.difference(listReceiver, notEnoughReceiver);

            // lay danh sach gameWinners
            //var listWinners = me.getPlayersJSON(tid, 'gameWinners', uid);

            logger.info("listJoinGame: ", listJoinGame, " => uid: ", uid);
            //logger.info("me.tables[tid].state: ", me.tables[tid]);

            let listPlayers = [];
            if (me.tables[tid].state === "IN_PROGRESS" || me.tables[tid].state === "END_GAME") {
                // TH: state = IN_PROGRESS
                /**
                 * Bàn đang chơi trả về danh sách người đang chơi + người đang ngồi
                 * -> push về cho chính người vừa ngồi xuống
                 */
                listPlayers = me.getPlayersJSON(tid, 'players', uid);
                listMembers     = _.union(listMembers, listPlayers);

                if (typeof channel.getMember(uid) !== "undefined"){
                    var receiverTmp = {uid: uid, sid: channel.getMember(uid)['sid']};

                    listReceiver.push(receiverTmp);
                }

                if (me.tables[tid].state === "IN_PROGRESS")
                    waitingsPlayer  = me.getPlayersJSON(tid, 'playersToAdd', uid);

                if (me.tables[tid].state === "END_GAME")
                    waitingsPlayer  = []; //me.getPlayersJSON(tid, 'playersToAdd', uid);
            }

            listMembers     = _.uniq(listMembers);
            listReceiver    = _.uniq(listReceiver);

            logger.info("listReceiver: ", listReceiver);
            logger.info("waitingsPlayer: ", waitingsPlayer);
            
            logger.info("1listMembers: ", listMembers);
            logger.info("me.getPlayersJSON(tid, 'players', uid): ", me.getPlayersJSON(tid, 'players', uid));
            logger.info("me.tables[tid].state: ", me.tables[tid].state);
            logger.info("1board: ", me.getTableJSON(tid, uid)?.board ?? []);
            logger.info("1currentPlayerTime: ", me.getTableJSON(tid, uid)?.currentPlayerTime ?? 0,);
            logger.info("1currentPlayerIndex: ", me.getTableJSON(tid, uid)?.currentPlayer ?? null);
            
            channelService.pushMessageByUids({
                route  : consts.GAME.ROUTER.JOIN_GAME,
                members : listMembers,
                players : me.getPlayersJSON(tid, 'players', uid),
                waitings: waitingsPlayer,
                state: me.tables[tid].state,
                board: me.getTableJSON(tid, uid)?.board ?? [],
                currentPlayerTime: me.getTableJSON(tid, uid)?.currentPlayerTime ?? 0,
                currentPlayerIndex: me.getTableJSON(tid, uid)?.currentPlayer ?? null,
                tournament_mode: true,
                tournament_id: me.tables[tid].tournament_id,
                timestamp: Math.floor(Date.now() / 1000)
            }, listReceiver, function(e) {
                if(e) {
                    logger.error(consts.GAME.ROUTER.JOIN_GAME, ': unable to push members ', e);
                }
                // begin: lấy danh sách người chơi chầu rìa
                //var members         = table.members; // danh sách người chơi có mặt trong bàn
                //var lobbyListTemp   = me.getDanhSachChauRia(members, listMembers);

                logger.info("table: " , table);
                logger.info("table.members: " , table.members);
                logger.info("listMembers: " , listMembers);
                
                // Create clean array of member IDs to avoid circular references in getChauRia
                var cleanTableMembers = table.members.map(function(member) {
                    return { id: member.id };
                });
                
                var chauria = me.getChauRia(cleanTableMembers, listMembers, channel);

                // bỏ qua những player trong nhóm không đủ tiền
                // chauria = _.difference(chauria, notEnoughReceiver);

                // Gộp 2 mảng lại với nhau
                var listAll = _.union(chauria, notEnoughReceiver);
                // Bỏ các phần tử trong nhóm không đủ tiền ra khỏi danh sách đc nhận message
                chauria = _.filter(listAll, function(obj){ return !_.findWhere(notEnoughReceiver, obj); });

                // Bắn dữ liệu về cho danh sách đang chầu rìa
                logger.info("uid: " , uid, " -> ban cho nhung nguoi chau ria3:", chauria);

                logger.info("1.All member: ", table.members);
                logger.info("1.listMembers: ", listMembers);
                logger.info("1.chauria: ", chauria);

                if (chauria.length > 0) {
                    logger.info("-> so nguoi chau ria(", chauria.length ,") > 0 -> pushMessage");
                    channelService.pushMessageByUids({
                        route: consts.GAME.ROUTER.JOIN_GAME,
                        members: listMembers,
                        players: listPlayers,
                        msg: me.getPlayerJSON(tid, uid, 'playersToAdd') || me.getPlayerJSON(tid, uid),
                        waitings: waitingsPlayer,
                        state: me.tables[tid].state,
                        board: me.getTableJSON(tid, uid)?.board ?? [],
                        timestamp: Math.floor(Date.now() / 1000),
                        currentPlayerTime: me.getTableJSON(tid, uid)?.currentPlayerTime ?? 0,
                        currentPlayerIndex: me.getTableJSON(tid, uid)?.currentPlayer ?? null,
                        tournament_mode: true,
                        tournament_id: me.tables[tid].tournament_id,
                    }, chauria, function (e) {
                        logger.info("-> SNG callback sau khi pushMessage cho chau ria");
                    });
                }

            });

            // check nếu số người chơi đủ theo player_capacity thì auto start SNG tournament
            // --------------------------------------------------------------------------------------
            var _listPlayerToAdd = me.getPlayersJSON(tid, 'playersToAdd', uid);
            var _playersToAddLength = _listPlayerToAdd.length;
            var tableObj = me.tables[tid];
            var tournament = me.tournaments[tableObj.tournament_id];
            logger.info("[addPlayer] tournament: ", tournament);
            
            // Nếu không tìm thấy trong active tournaments, tìm trong waiting tournaments
            if (!tournament) {
                for (var key in me.waitingTournaments) {
                    if (me.waitingTournaments[key].id === tableObj.tournament_id) {
                        tournament = me.waitingTournaments[key];
                        break;
                    }
                }
            }
            
            var requiredPlayers = tournament ? tournament.config.player_capacity : table.maxPlayers;
            
            logger.info(" --> So Nguoi choi ngoi xuong ghe: " + _playersToAddLength + "/" + requiredPlayers);
            
            if(_playersToAddLength >= requiredPlayers) {
                logger.info("SNG Tournament đủ số người chơi: ", _playersToAddLength, ">=", requiredPlayers, " - Auto start tournament");
                
                // Update tournament status và start tournament
                if (tournament) {
                    tournament.status = 'starting';
                    
                    // Start tournament với 5 giây countdown
                    setTimeout(function() {
                        me.startTournament(tournament.id, function(startResult) {
                            if (startResult.success) {
                                logger.info("SNG Tournament started successfully:", tournament.id);
                            } else {
                                logger.error("Failed to start SNG tournament:", startResult.error);
                            }
                        });
                    }, consts.GAME.TIMER.SNG_COUNTDOWN * 1000); // 5 seconds countdown
                    
                    // Broadcast countdown message
                    var channel = channelService.getChannel(tid, false);
                    if (channel) {
                        channel.pushMessage({
                            route: consts.GAME.ROUTER.SNG_TOURNAMENT_STATUS,
                            tournament_id: tournament.id,
                            status: 'STARTING',
                            message: 'Tournament is full! Starting in ' + consts.GAME.TIMER.SNG_COUNTDOWN + ' seconds...',
                            countdown: consts.GAME.TIMER.SNG_COUNTDOWN,
                            current_players: _playersToAddLength,
                            required_players: requiredPlayers,
                            timestamp: Math.floor(Date.now() / 1000)
                        });
                    }
                }
            } else {
                logger.info("SNG Tournament cần thêm người chơi: ", (requiredPlayers - _playersToAddLength), " người");
            }

            // set chế độ đang không hoạt động
            // -----------------------------------------------------------------------------------------------------------
            table.isSitDown = false;
            logger.info("[end] addPlayer uid ", uid, " đã ngồi xong with actorNr:", index, " and isSitDown: ", table.isSitDown);
            cb();
        });
    });
};

/**
 * Helper methods cloned from TableService for SNG compatibility
 */
SngTableService.prototype.getPlayerIndex = function(tid, uid, type) {
    if (!this.tables[tid]) return null;
    
    var players = this.tables[tid].table[type] || [];
    for (var i = 0; i < players.length; i++) {
        if (players[i] && players[i].id === uid) {
            return i;
        }
    }
    return null;
};

SngTableService.prototype.getPlayersJSON = function(tid, type, uid) {
    if (!this.tables[tid]) return [];
    
    var players = this.tables[tid].table[type] || [];
    return players.filter(function(player) {
        return player && !player.isEmpty;
    }).map(function(player) {
        // Return clean player object without circular references
        return {
            id: player.id,
            playerName: player.playerName,
            username: player.username,
            nick_name: player.nick_name,
            avatar: player.avatar,
            level: player.level,
            vippoint: player.vippoint,
            exp: player.exp,
            balance: player.balance,
            chips: player.chips,
            type: player.type,
            isState: player.isState,
            actorNr: player.actorNr,
            isEmpty: player.isEmpty,
            isPlaying: player.isPlaying,
            cards: player.cards,
            folded: player.folded,
            allIn: player.allIn,
            talked: player.talked,
            isSmallBlind: player.isSmallBlind,
            isBigBlind: player.isBigBlind,
            isDealer: player.isDealer,
            bet: player.bet,
            roundBet: player.roundBet
        };
    });
};

SngTableService.prototype.getPlayerJSON = function(tid, uid, type) {
    if (!this.tables[tid]) return null;
    
    var players = this.tables[tid].table[type || 'playersToAdd'] || [];
    var player = players.find(function(player) {
        return player && player.id === uid;
    });
    
    if (!player) return null;
    
    // Return clean player object without circular references
    return {
        id: player.id,
        playerName: player.playerName,
        username: player.username,
        nick_name: player.nick_name,
        avatar: player.avatar,
        level: player.level,
        vippoint: player.vippoint,
        exp: player.exp,
        balance: player.balance,
        chips: player.chips,
        type: player.type,
        isState: player.isState,
        actorNr: player.actorNr,
        isEmpty: player.isEmpty,
        isPlaying: player.isPlaying,
        cards: player.cards,
        folded: player.folded,
        allIn: player.allIn,
        talked: player.talked,
        isSmallBlind: player.isSmallBlind,
        isBigBlind: player.isBigBlind,
        isDealer: player.isDealer,
        bet: player.bet,
        roundBet: player.roundBet
    };
};

/* SngTableService.prototype.getTableJSON = function(tid, uid) {
    if (!this.tables[tid]) return null;
    return this.tables[tid].table;
}; */
SngTableService.prototype.getTableJSON = function(tid, uid){
    if(!this.tables[tid]){
        return;
    }
    var table = this.tables[tid];
    logger.info("[sngTableService.getTableJSON] tid: ", tid, " => table.table: ", table.table);
    return {
        state           : table.state,
        id              : (table.table && table.table.game && table.table.game.id ? table.table.game.id : undefined),
        tid             : tid,
        creator         : table.creator,
        dealer          : table.table.dealer,
        smallBlind      : table.table.smallBlind,
        bigBlind        : table.table.bigBlind,
        minPlayers      : table.table.minPlayers,
        maxPlayers      : table.table.maxPlayers,
        minBuyIn        : table.table.minBuyIn,
        maxBuyIn        : table.table.maxBuyIn,
        gameMode        : table.table.gameMode,
        isShowBoard     : table.table.isShowBoard,
        players         : this.getPlayersJSON(tid, 'players', uid),
        playersToRemove : this.getPlayersJSON(tid, 'playersToRemove', uid),
        playersToAdd    : this.getPlayersJSON(tid, 'playersToAdd', uid),
        gameWinners     : this.getPlayersJSON(tid, 'gameWinners', uid),
        playersToNotEnough     : this.getPlayersJSON(tid, 'playersToNotEnough', uid),
        actions         : table.table.actions,
        game            : stripDeck(table.table.game, ['deck', 'id']),
        board           : (table.table.game && table.table.game.board) ? table.table.game.board : [],
        currentPlayer   : table.table.currentPlayer,
        currentPlayerTime: table.table.currentPlayerTime, // 11/02/2025 => added
    };
};

function stripDeck(obj, props){
    var out = {};
    for(var key in obj){
        if(props.indexOf(key) == -1){
            out[key] = obj[key];
        }
    }
    return out;
}

SngTableService.prototype.getReceiver = function(arrList, channel) {
    var _newArr1 = [];
    for(var i = 0; i < arrList.length; i++) {
        _newArr1.push(arrList[i]["id"]);
    }

    var dataReturn = [];
    for(var m = 0; m < _newArr1.length; m++) {
        if (typeof channel.getMember(_newArr1[m]) !== "undefined") {
            dataReturn.push(channel.getMember(_newArr1[m]));
        }
    }
    return dataReturn;
};

SngTableService.prototype.getChauRia = function(arr1, arr2, channel) {
    var _newArr1 = [];
    for(var i = 0; i < arr1.length; i++) {
        _newArr1.push(arr1[i]["id"]);
    }

    var _newArr2 = [];
    for(var j = 0; j < arr2.length; j++) {
        _newArr2.push(arr2[j]["id"]);
    }

    var lobbyList = _.difference(_newArr1, _newArr2);
    var chauria = [];
    for(var m = 0; m < lobbyList.length; m++) {
        var gMb = channel.getMember(lobbyList[m]);
        if (gMb && gMb["sid"] !== undefined){
            var arrTemp = {uid: lobbyList[m], sid: gMb["sid"]};
            chauria.push(arrTemp);
        }
    }
    return chauria;
};

SngTableService.prototype.pushPlayerInfo = function(tid, uid, cb) {
    logger.info("[sngTableService.pushPlayerInfo] tid: ", tid, " => uid: ", uid);
    var me = this;
    
    me.app.rpc.db.dbRemote.getPlayerById('*', uid, async (e, code, info) => {
        logger.info("[sngTableService.pushPlayerInfo] e:", e, " => code: ", code, ' => info: ', info);

        var channelService = me.app.get('channelService');
        var channel = channelService.getChannel(tid, false);
        if (!channel || !channel.getMember(uid)) return;
        
        channelService.pushMessageByUids({
            route: consts.GAME.ROUTER.UPDATE_MYSELF,
            msg: info
        }, [{
            uid: uid,
            sid: channel.getMember(uid)['sid']
        }], function (e) {
            if (e) {
                logger.error('[sngTableService.pushPlayerInfo] unable to push player info ', e);
            }
            logger.info("[sngTableService.pushPlayerInfo] done [ok]");
            cb("done");
        });
    });
};


SngTableService.prototype.checkAndUpdateDataPlayer = function (info) {
    logger.info("[checkAndUpdateDataPlayer] info: ", info);
    var self = this;
    const balance = info?.balance ?? 0;
    logger.info("[checkAndUpdateDataPlayer] balance: ", balance);
    if (balance < 0) {
        logger.info("[checkAndUpdateDataPlayer] Truờng hợp số tiền < 0 ", info);
        info.balance    = 0;
        const userId = info?.id ?? 0; // info?.user_id ?? '';
        var dataUpdate  = {
            balance: 0
        };
        self.app.rpc.db.dbRemote.updatePlayer('*', userId, dataUpdate, function (err, code, res) {
            logger.info("[checkAndUpdateDataPlayer] Số tiền đã được cập nhật về 0 với res: ", res, ' -> err: ', err, ' -> code: ', code);
        });

        // Cập nhật lại dữ liệu trong cached
        // --------------------------------------------------------------------------------------
        self.app.rpc.manager.userRemote.onUpdatePlayer(null, userId, info, function (user) {
            //logger.info("thong tin user from useCached sau khi updatePlayer: ", user);
        });
    }
};

SngTableService.prototype.pushPlayerInfo2 = function(tid, uid, channelService, channel, cb) {
    logger.info("[sngTableService.pushPlayerInfo2] tid: ", tid, " -> uid: ", uid);

    var self = this;
    // userDao.getPlayerByUid(uid, function (e, info) {
    self.app.rpc.db.dbRemote.getPlayerById('*', uid, async (e, code, info) => {
        logger.info("[sngTableService.pushPlayerInfo2] e: ", e, " -> code: ", code, ' -> info: ', info);

        // - Check số tiền hiện tại < 0 thì cập nhật lại về 0 và return về cho client
        // ------------------------------------------------------------------------------------------
        self.checkAndUpdateDataPlayer(info);

        // Lấy thông tin và push về cho chính client đó
        // ------------------------------------------------------------------------------------------
        if(!channel || !channel.getMember(uid)) return;

        channelService.pushMessageByUids({
            route: consts.GAME.ROUTER.UPDATE_MYSELF,
            msg: info
        }, [{
            uid: uid,
            sid: channel.getMember(uid)['sid']
        }], function (e) {
            if (e) {
                logger.error('[sngTableService.pushPlayerInfo2] unable to push player info ', e, ' -> message: ', e.message, ' -> stack: ', e.stack);
            }
            cb("done");
        });
    });
};


/**
 * Start the game
 *
 * @param {Object} tid id of an existing table
 * @param {function} cb callback
 *
 */
SngTableService.prototype.startGame = function(tid, cb) {
    var table = this.tables[tid];
    if(!table){
        return cb('table-not-found');
    }
    //if(table.state != 'JOIN'){
    logger.info("[startGame] table.state: ", table.state);
    //if(table.state != 'JOIN' || table.state != 'END_GAME'){
    if(table.state == 'IN_PROGRESS'){
        return cb('table-not-ready');
    }
    logger.info('[startGame] table.table.active: ', table.table.active);
    if(table.table.active){
        return cb('table-still-active');
    }
    logger.info('[startGame] table.table.playersToAdd.length: ', table.table.playersToAdd.length);
    if(table.table.playersToAdd.length < table.table.minPlayers) {
        logger.info('[startGame] table.table.playersToAdd: ', table.table.playersToAdd);
        return cb('not-enough-players');
    }
    if(table.table.playersToAdd.length > table.table.maxPlayers){
        return cb('too-many-players');
    }
    // remove chips from user for buy in
    table.table.StartGame();
    this.app.get('channelService').getChannel(tid, true).pushMessage({
        route   : 'onUpdateUsers',
        members : table.table.members
    });
    this.broadcastGameState(tid);
    cb();
};


SngTableService.prototype.saveResults = async function(tid, cb) {

    var me = this;
    let lid = 0;
    try {

        if(!this.tables[tid]) {
            cb('table-not-found');
        }
        var table       = this.tables[tid];
        var tableInfo   = me.getTableJSON(tid);
        var users       = tableInfo.players;

        logger.info("[sngTableService.saveResults] tableInfo: ", tableInfo);
        logger.info("[sngTableService.saveResults] users: ", users);
        if (tableInfo.gameWinners.length > 0) {

            // Begin: Push log to rabbitmq
            // ---------------------------------------------------------------------------------------------------
            // const logsPlayer = users.map(user => user.id);
            const userIds = users.map(user => user.id);
            logger.info("[sngTableService.saveResults] userIds: ", userIds);
            const logsGamePayload = {
                logs: utils.jsonEndcode(tableInfo).toString(),
                players: userIds // logsPlayer,
            }
            logger.info("[sngTableService.saveResults] logsGamePayload: ", logsGamePayload);

            const rabbitmqService = me.app.get('rabbitmqService');
            try {
                // Kiểm tra kết nối
                if (!rabbitmqService.channel || !rabbitmqService.connection) {
                    await rabbitmqService.connect();
                }

                await rabbitmqService.sendToQueue(
                    consts.GAME.LOGS.EX,
                    utils.jsonEndcode(logsGamePayload).toString()
                );
                logger.info("[sngTableService.saveResults] Sent log to RabbitMQ successfully");
            } catch (rabbitErr) {
                logger.error("[sngTableService.saveResults] Failed to send log to RabbitMQ: ", rabbitErr);
                // Implement backup solution (store to DB or file)
                // await storeLogToBackup(logsGamePayload);
            }
            // End: Push log to rabbitmq
            // ---------------------------------------------------------------------------------------------------

            var expService = this.app.get('expService');
            let beforePlayersDetails = [];

            async.waterfall([
                function (callback) {
                    // Step 0: Get players information by UIDs before updating logs
                    // ----------------------------------------------------------------------------------------------
                    // const userIds = users.map(user => user.id);
                    logger.info("[sngTableService.saveResults][Step 0] Getting player info for UIDs: ", userIds);
                    me.app.rpc.db.dbRemote.getPlayersByIds('*', userIds, (err0, code0, playersList) => {
                        if (err0) {
                            logger.error("[sngTableService.saveResults] Failed to get players by IDs: ", err0);
                            return callback(err0);
                        }

                        logger.info("[sngTableService.saveResults] Retrieved players info: ", playersList);

                        // Add player information to the table info for more detailed logging
                        if (playersList && Array.isArray(playersList)) {
                            // tableInfo.playersDetails = playersList;
                            beforePlayersDetails = playersList;
                        }

                        callback(null);
                    });
                },
                function (callback) {
                    // Step 1: Ghi Logs vào database
                    // ----------------------------------------------------------------------------------------------
                    me.app.rpc.db.dbRemote.createLogs('*', users, tableInfo, callback);
                },
                // function (users, callback) {
                function (code1, dataCb, callback) {
                    logger.info("[sngTableService.saveResults][Step 2] users sau khi đã ghi log: ", dataCb, ' -> code1: ', code1);
                    // Step 2: Lấy thông tin users vừa ghi log và cập nhật lại tiền ở step 1
                    // ----------------------------------------------------------------------------------------------
                    var listUid = dataCb?.data ?? []; // đây là mảng uids = [1, 2] => tương đồng với userIds ở trên
                    lid = dataCb?.lid ?? 0;

                    // bắn sự kiện end game cho các event listener đang đăng ký: transactions, missions
                    // ----------------------------------------------------------------------------------------------
                    me.app.event.emit('endGame', {
                        logs: tableInfo,
                        lid: lid,
                        before_players: beforePlayersDetails
                    });

                    // Lấy lại thông tin danh sách players sau khi cập nhật lại tiền từ database
                    // ----------------------------------------------------------------------------------------------
                    logger.info("[sngTableService.saveResults] listUid: ", listUid);
                    me.app.rpc.db.dbRemote.getPlayersByIds('*', listUid, (e2, code2, res) => {
                        logger.info("[sngTableService.saveResults] getPlayersByIds >> e2: ", e2, ' -> code2: ', code2, ' -> res: ', res);
                        callback(null, listUid, res);
                    });
                },
                function (uids, usersCb, callback) {
                    logger.info("[sngTableService.saveResults][Step 3] uids: ", uids, " => usersCb: ", usersCb);
                    if (usersCb && usersCb.length > 0) {
                        // cập nhật lại 1 số thuộc tính của người thắng: số tiền thắng , bộ bài vào tables database => không cần nếu gameaMode = sng
                        if (tableInfo.gameMode !== 'sng') {
                            me.__updatePlayJson(usersCb, tableInfo, function (e, _res) {
                                callback(null, usersCb);
                            });
                        } else {
                            callback(null, usersCb);
                        }
                    }
                },
                function (arr, callback) {
                    logger.info("[sngTableService.saveResults][Step 5] arr: ", arr);
                    // Step 5: Cập nhật lại info vào userCached
                    // ----------------------------------------------------------------------------------------------
                    // const playerCount = arr.length || 0;
                    for (var k = 0; k < arr.length; k += 1) {
                        var user = arr[k];
                        logger.info("[sngTableService.saveResults][Step 5] >> user: ", user);
                        me.app.rpc.manager.userRemote.onUpdatePlayer(null, user.id, user, function (user) {
                            //logger.info("thong tin user from useCached sau khi updatePlayer: ", user);
                        });
                    }
                    callback(null, "done");
                }
            ], function (err, result) {
                logger.info("[sngTableService.saveResults] in saveResults err: ",err," => result: ", result, ' -> message: ', err?.message, ' -> stack: ', err?.stack);
                // Begin: tính toán điểm kinh nghiệm
                // -------------------------------------------------------------------------------------------------------------------------------------
                const players = tableInfo.gameWinners[0]?.players || [];
                const totalPlayer = players.length || 0;

                for(let i = 0; i < users.length; i += 1) {
                    const user = users[i];
                    const uid = parseInt(user.id, 10);

                    // Tìm thông tin người chơi trong danh sách người thắng
                    const playerData = _.findWhere(players, {id: uid});
                    const isWin = playerData ? playerData.isWinner : false;

                    if (logger.isInfoEnabled()) {
                        logger.info(`[sngTableService.saveResults] Tính điểm EXP cho user ${uid}, isWin: ${isWin}, totalPlayer: ${totalPlayer}`);
                    }

                    // Gọi service tính điểm kinh nghiệm
                    expService.calculateGameResultExp({
                        playerId: uid,
                        isWin: isWin,
                        playerCount: totalPlayer,
                        smallBlind: tableInfo.smallBlind,
                        playerLevel: user.level
                    }, function (err, code, result) {
                        logger.info("[sngTableService.saveResults] calculateGameResultExp err: ", err, " => code: ", code, " => result: ", result);
                        if (err) {
                            logger.error(`[sngTableService.saveResults] Lỗi khi tính điểm EXP cho user ${uid}:`, err);
                            return;
                        }

                        if (logger.isDebugEnabled()) {
                            logger.debug(`[sngTableService.saveResults] User ${uid} ${isWin ? 'thắng' : 'thua'}, nhận được ${result?.expValue || result?.expAdded} điểm EXP`);
                        }
                    });
                }
                // -------------------------------------------------------------------------------------------------------------------------------------
                // End: tính toán điểm kinh nghiệm

                return cb();
            });

        }else{
            return cb();
        }
        // Add logs to mysql database
        // ---------------------------------------------------------------------------------------------------

    } catch (err) {
        logger.error("[tableService.saveResults] Error:", err);
        return cb(err);
    }
};

SngTableService.prototype.__updatePlayJson = function (res, tableInfo, cb) {
    var gameWinner, uidWinner, playJson, playerList, winnerPlayer, amountWin, cardHands, code;
    gameWinner      = tableInfo.gameWinners;
    uidWinner       = gameWinner[0].id;
    playerList      = gameWinner[0].players;
    winnerPlayer    = _.findWhere(playerList, {id: uidWinner});

    logger.info("[sngTableService.__updatePlayJson] tableInfo>>gameWinner with uid ", uidWinner ," >> gameWinner", gameWinner);
    logger.info("[sngTableService.__updatePlayJson] tableInfo>>gameWinner>>playerList: ", playerList);
    logger.info("[sngTableService.__updatePlayJson] tableInfo>>gameWinner>>playerList>>winnerPlayer: ", winnerPlayer);

    amountWin = winnerPlayer.amount;
    cardHands = winnerPlayer.hand.cards;
    code      = winnerPlayer.hand.code;

    logger.info("[sngTableService.__updatePlayJson] tableInfo>>res: ", res);

    // var userWinner = _.findWhere(res, {uid: uidWinner});
    var userWinner = _.findWhere(res, {id: uidWinner});

    var biggestWon, bestHand, codeWin;

    logger.info("[sngTableService.__updatePlayJson] tableInfo>>userWinner: ", userWinner);

    // playJson = userWinner.playJson;
    // playJson = userWinner?.play_json;
    playJson = userWinner?.properties?.play_json || {};

    playJson = JSON.parse(playJson);
    logger.info("[sngTableService.__updatePlayJson] tableInfo>>userWinner>>playJson: ", playJson);

    //logger.info("__updatePlayJson >> typeof playJson: ", utils.empty(playJson));
    logger.info("[sngTableService.__updatePlayJson] typeof playJson.biggestWon: ", typeof playJson.biggestWon);
    logger.info("[sngTableService.__updatePlayJson] playJson.biggestWon: ", playJson.biggestWon);

    //if (playJson != null){
    //if (typeof playJson.biggestWon !== "undefined" || playJson != null){
    if (typeof playJson.biggestWon != "undefined"){
        logger.info("[sngTableService.__updatePlayJson] ===> Co Du Lieu ");
        biggestWon = playJson.biggestWon;
        if (amountWin > playJson.biggestWon){
            biggestWon = amountWin;
        }
        codeWin = playJson.codeWin;

        if (typeof playJson.bestHand != "undefined") {
            bestHand = playJson.bestHand;
        }else{
            bestHand = cardHands;
        }

        if (typeof playJson.codeWin == "undefined") {
            codeWin = code;
            bestHand = cardHands;
        }

        if (code > playJson.codeWin){
            codeWin = code;
            bestHand = cardHands;
        }
    } else {
        logger.info("[sngTableService.__updatePlayJson] ===> Khong Co Du Lieu ");
        biggestWon  = amountWin;
        bestHand    = cardHands;
        codeWin     = code;
    }

    let tmpPlayJson = {
        biggestWon: biggestWon,
        bestHand: bestHand,
        codeWin:codeWin
    };

    let arrPro = {
        play_json: JSON.stringify(tmpPlayJson)
    };

    logger.info("[sngTableService.__updatePlayJson] arrPro: ", arrPro);

    // userDao.updateProperties(uidWinner, arrPro, function (e, _res) {
    pomelo.app.rpc.db.dbRemote.updateProperties('*', uidWinner, arrPro, (e, code, _res) => {
        logger.info("[sngTableService.__updatePlayJson] Sau khi update thanh cong playJson: e: ", e, " code: ", code, " => _res:", _res);
        cb(null, _res);
    });

};


SngTableService.prototype.handleGameState = function(tid, cb){
    var me = this;
    var table = me.tables[tid];
    // check table
    if (table) {
        if (table.table && table.table.game && table.table.game.roundName == 'GameEnd' && table.state == 'IN_PROGRESS' && table.table.active) {
            logger.info("[SngTableService.handleGameState] table.table.game.roundName: ", table.table.game.roundName, ' -> call endGame');

            // Gửi thông tin kết quả ván chơi trước khi gọi endGame
            me.broadcastEndGameResults(tid);

            // Sau đó mới gọi endGame để xử lý kết thúc ván chơi
            me.endGame(tid, cb);
        } else {
            // logger.info("table.table.player: ", table.table.players);
            var tmpArr = [];
            _.each(table.table.players, function (item) {
                var _item = _.pick(item, "id", "playerName", "chips", "folded", "folded", "allIn", "talked");
                tmpArr.push(_item);
            });

            logger.info("tmpArr: ", tmpArr);

            me.app.get('channelService').getChannel(tid, true).pushMessage({
                route: 'onUpdateUsers',
                action: 'IN_PLAY',
                members: tmpArr //table.table.members
            });
            me.broadcastGameState(tid);
            cb();
        }
    }else{
        cb();
    }
};

/**
 * Perform game action (delegate to table)
 */
// SngTableService.prototype.performAction = function(tid, uid, action, callback) {
//     var table = this.tables[tid];
//     if (!table) {
//         return callback('table-not-found');
//     }
    
//     if (table.state !== 'IN_PROGRESS') {
//         return callback('tournament-not-in-progress');
//     }
    
//     // Delegate to table's performAction method
//     // This will be implemented in SngTable class
//     table.table.performAction(uid, action, callback);
// };

/**
 * Perform a game action
 *
 * @param {string} tid table id
 * @param {string} uid userId to add to the table
 * @param {object} action an object containing the action type and optionally the amount of chips
 * @param {function} cb callback
 *
 */
SngTableService.prototype.performAction = function(tid, uid, action, cb){
    logger.info("performAction >> tid: ", tid, " -> uid: ", uid, " -> action: ", action);
    var me = this;
    var table = this.tables[tid];

    if(!table){
        return cb('table-not-found');
    }
    if(table.state != 'IN_PROGRESS'){
        return cb('game-not-ready');
    }
    if(me.getPlayerIndex(tid, uid) != table.table.currentPlayer){
        return cb('not-your-turn');
    }
    if(me.getPlayerJSON(tid, uid).folded == true){
        return cb('already-folded');
    }
    if(action.action == 'bet' && isNaN(action.amt)){
        return cb('invalid-bet-amt');
    }

    // check thêm trạng thái có đang xử lý action nào không, tránh việc xử lý liên tiếp
    if(table.table.isAction){
        logger.info("Đang xử lý action : ", action.action);
        return cb("action-is-processing");
    }
    // bắt đầu set trạng thái đang hoạt động
    // ------------------------------------------------------------------------------------------
    table.table.isAction = true;
    //logger.debug("app||services||tableService: in tables: ", table.table);

    // perform action
    if(action.action == 'call'){
        table.table.players[table.table.currentPlayer].Call();
    }else if(action.action == 'bet'){
        table.table.players[table.table.currentPlayer].Bet(parseInt(action.amt));
        // fire emit event eventPlayerBet (phần nhiệm vụ)
        me.app.event.emit('eventPlayerBet', {
            playerId: uid,
            betAmount: parseInt(action.amt)
        });
    }else if(action.action == 'check'){
        table.table.players[table.table.currentPlayer].Check();
    }else if(action.action == 'allin'){
        table.table.players[table.table.currentPlayer].AllIn();
    }else if(action.action == 'fold'){
        table.table.players[table.table.currentPlayer].Fold();
    } else {
        table.table.isAction = false;
        return cb('invalid-action');
    }
    table.table.stopTimer();
    // chuyển trạng thái của isAction = false không hoạt động
    // ------------------------------------------------------------------------------------------
    table.table.isAction = false;

    logger.debug('player ' + uid + ' executed action ' + action.action + ' on table ' + tid + ' with state ' + table.state);

    me.handleGameState(tid, function(e) {
        if(e){
            return cb(e);
        }
        cb();
    });
};


/**
 * End game and broadcast result to clients
 *
 * @param {string} tid table id
 * @param {function} cb callback
 *
 */
SngTableService.prototype.endGame = function(tid, cb) {
    logger.info("[tableService.endGame] tid: ", tid);
    var me = this;
    if(!me.tables[tid]){
        cb('table-not-found');
        return;
    }
    var table = me.tables[tid];
    if(table.table.game.roundName != 'GameEnd'){
        cb('not-game-end');
        return;
    }
    table.table.active = false;
    table.table.stopTimer();
    me.saveResults(tid, function(e){
        if(e){
            cb(e);
            return;
        }
        var channelService = me.app.get('channelService');
        channelService.getChannel(tid, false).pushMessage({
            route   : 'onUpdateUsers',
            members : table.table.members
        });

        // init new game
        table.table.initNewGame("END_GAME");

        setTimeout(function() {

            logger.info("[tableService.endGame] đợi hết timeout để send command endGame xuống client");

            me.broadcastGameState(tid);

            // begin: push command for client clear table
            // --------------------------------------------------------------------------------------------------
            logger.info("[tableServices] -> add Job send command clear addJobClearGame -----------------");
            me.app.rpc.game.sngTableRemote.addJobClearGame(null, tid, consts.GAME.TIMER.WAIT_CLEAR ,function () {});


            // begin: check time để auto start - 8s
            // --------------------------------------------------------------------------------------------------
            logger.info("[tableServices] -> add Job addJobStartGame tid: ", tid, " -> consts.GAME.TIMER.WAIT_START: ", consts.GAME.TIMER.WAIT_START);
            me.app.rpc.game.sngTableRemote.addJobStartGame(null, tid, consts.GAME.TIMER.WAIT_START ,function () {});

        }, 2500);

        cb();
    });
};


/**
 *
 * - push command clear hết dữ liệu từ ván trước để chuẩn bị start ván mới
 * - sử dụng trước khi bắt đầu ván mới
 * @param tid
 */
SngTableService.prototype.pushTableClear = function (tid, cb){
    logger.info("[pushTableClear] send command clear data to table ", tid);
    var i               = 0;
    var me              = this;
    var channelService  = me.app.get('channelService');
    var channel         = channelService.getChannel(tid, false);

    // udpate check table not found
    if(me.tables[tid]) {
        // check player at 0 position not undefined
        if (me.tables[tid].table.members[i] !== undefined) {
            var uid         = me.tables[tid].table.members[i].id;
            var receives    = me.getPlayersJSON(tid, 'playersToAdd', uid);

            if (receives.length <= 0){
                receives = me.getPlayersJSON(tid, 'players', uid);
            }

            logger.info("[pushTableClear] Receives: ", receives);

            // logger.info("[pushTableClear] getTableJSON: ", me.getTableJSON(tid, uid));

            if (channel.getMember(uid)) {
                me.app.get('channelService').getChannel(tid, false).pushMessage({
                    route: consts.GAME.ROUTER.GAME_CLEAR, //'onClearTable',
                    members: me.getPlayersJSON(tid, 'playersToAdd', uid)
                    //msg: me.getPlayersJSON(tid, 'playersToAdd', uid)
                });
            }
        }// end check player with i = 0 not undefined
    }
    cb();
};

/**
 * Broadcast game state by iteratively pushing game details to clients
 *
 * @param {string} tid id
 *
 */
SngTableService.prototype.broadcastGameState = function(tid) {
    logger.info("[tableService.broadcastGameState] tid: ", tid);
    var i = 0;
    var me = this;
    var channelService = me.app.get('channelService');
    var channel = channelService.getChannel(tid, false);

    function broadcast() {
        if(i == me.tables[tid].table.members.length){
            if(me.tables[tid].state == 'IN_PROGRESS' && me.tables[tid].table.active){
                me.tables[tid].table.startTimer();
            }
            return;
        }

        // var uid = me.tables[tid].table.members[i].id;
        let uid = 0
        if (me.tables[tid].table.members[i]) {
            uid = me.tables[tid].table.members[i].id;
        } else {
            uid = 0;
        }

        if(channel.getMember(uid)){
            channelService.pushMessageByUids({
                route  : 'onTableEvent',
                msg    : me.getTableJSON(tid, uid),
                timestamp: Math.floor(Date.now() / 1000)
            }, [{
                uid : uid,
                sid : channel.getMember(uid)['sid']
            }], function(){
                ++i;
                broadcast();
            });
        }else{
            ++i;
            broadcast();
        }

        // Không reset gameWinners ở đây nữa, để đảm bảo thông tin người thắng được giữ lại cho event onEndGame

    } // end function broadcast

    broadcast();

};

/**
 * Hàm push action turn cuối cùng khi kết thúc game
 * - Để cho các player khác cập nhật được action vừa thực hiện của player cuối cùng
 * @param tid
 */
SngTableService.prototype.broadcastEndGameState = function(tid) {

    var i               = 0;
    var me              = this;
    var channelService  = me.app.get('channelService');
    var channel         = channelService.getChannel(tid, false);

    // check table ton tai moi thuc hien
    if(me.tables[tid]) {
        if (me.tables[tid].table.members[i] !== undefined) {
            var uid = me.tables[tid].table.members[i].id;

            logger.info("[sngTableService] >> broadcastEndGameState ", me.getTableJSON(tid, uid));

            if (channel.getMember(uid)) {
                me.app.get('channelService').getChannel(tid, false).pushMessage({
                    //route: consts.GAME.ROUTER.GAME_EVENT, //'onTableEvent',
                    route: consts.GAME.ROUTER.END_TURN,
                    msg: me.getTableJSON(tid, uid)
                });
            }
        }// end check danh sach thanh vien cua ban con co nguoi
    }
};

/**
 * Hàm gửi kết quả cuối cùng của ván chơi đến tất cả người chơi
 * - Gửi thông tin chi tiết về kết quả ván chơi (gameWinners, sidepots, ...)
 * - Được gọi sau broadcastEndGameState để client có thể hiển thị kết quả ván chơi
 * @param {string} tid - Table ID
 */
SngTableService.prototype.broadcastEndGameResults = function(tid) {
    var me = this;
    var channelService = me.app.get('channelService');
    var channel = channelService.getChannel(tid, false);

    // Kiểm tra bàn tồn tại
    if (!me.tables[tid]) {
        logger.error("[sngTableService] >> broadcastEndGameResults: Table not found", tid);
        return;
    }

    // Lấy thông tin chi tiết của bàn chơi
    var tableInfo = me.getTableJSON(tid);
    var table = me.tables[tid].table;

    // Xử lý trường hợp đặc biệt: Nếu không có người thắng nhưng có sidepot
    if ((!tableInfo.gameWinners || tableInfo.gameWinners.length === 0) &&
        tableInfo.game.sidepots && tableInfo.game.sidepots.length > 0) {

        logger.info("[sngTableService] >> broadcastEndGameResults: No winners found but sidepots exist. Adding default winner.");

        // Tìm người chơi còn lại không fold
        var activePlayers = [];
        for (var i = 0; i < table.players.length; i++) {
            if (!table.players[i].folded) {
                activePlayers.push(table.players[i]);
            }
        }

        // Nếu chỉ còn một người chơi không fold, họ là người thắng
        if (activePlayers.length === 1) {
            var winner = activePlayers[0];
            var totalPot = 0;

            // Tính tổng pot từ tất cả sidepots
            for (var j = 0; j < tableInfo.game.sidepots.length; j++) {
                totalPot += tableInfo.game.sidepots[j].amount;
            }

            // Tạo danh sách người chơi cho winner
            var playersList = [];
            for (var k = 0; k < table.players.length; k++) {
                var player = table.players[k];
                var playerInfo = {
                    id: player.id,
                    playerName: player.playerName,
                    actorNr: player.actorNr,
                    cards: player.cards,
                    hand: player.hand,
                    folded: player.folded,
                    allIn: player.allIn,
                    chips: player.chips,
                    oldChips: player.oldChips,
                    amount: player.id === winner.id ? totalPot : -Math.abs(player.oldChips - player.chips),
                    isWinner: player.id === winner.id
                };
                playersList.push(playerInfo);
            }

            // Thêm người thắng vào gameWinners
            tableInfo.gameWinners = [{
                playerName: winner.playerName,
                id: winner.id,
                amount: totalPot,
                potType: "main pot",
                potIndex: 0,
                hand: winner.hand,
                chips: winner.chips,
                players: playersList,
                broad: tableInfo.game.board,
                sidepots: tableInfo.game.sidepots
            }];

            logger.info("[sngTableService] >> broadcastEndGameResults: Added default winner: " + winner.playerName + " with amount: " + totalPot);
        }
    }

    // Đảm bảo có thông tin gameWinners và sidepots
    if (!tableInfo.gameWinners || tableInfo.gameWinners.length === 0) {
        logger.warn("[sngTableService] >> broadcastEndGameResults: No winners found for table", tid);
    }

    logger.info("[sngTableService] >> broadcastEndGameResults: Sending end game results for table", tid);

    // Gửi thông tin kết quả ván chơi đến tất cả người chơi trong bàn
    channel.pushMessage({
        route: consts.GAME.ROUTER.END_GAME,
        msg: tableInfo
    });
};

/**
 * Shuffles an array
 *
 * @param {array} ary an array
 *
 */
SngTableService.prototype.shuffle = function(ary) {
    var currentIndex = ary.length, temporaryValue, randomIndex;
    while(0 !== currentIndex){
        randomIndex = Math.floor(Math.random() * currentIndex);
        currentIndex -= 1;
        temporaryValue = ary[currentIndex];
        ary[currentIndex] = ary[randomIndex];
        ary[randomIndex] = temporaryValue;
    }
    return ary;
};


/**
 * Broadcast tournament update to all players
 */
SngTableService.prototype.broadcastTournamentUpdate = function(tableId, eventType, data) {
    logger.info("[broadcastTournamentUpdate] tableId: ", tableId, " => eventType: ", eventType, " => data: ", data);
    var channelService = this.app.get('channelService');
    var channel = channelService.getChannel(tableId, false);
    
    if (channel) {
        channel.pushMessage({
            route: consts.GAME.ROUTER.SNG_TOURNAMENT_UPDATE,
            event_type: eventType,
            data: data || {}
        });
    }
};

/**
 * Unregister tournament (for rollback purposes)
 */
SngTableService.prototype.unregisterTournament = function(uid, tournamentId, callback) {
    // Implementation for rollback scenarios
    this.leaveTournament(uid, tournamentId, callback);
};

/**
 * Helper function to find tournament configuration
 * @param {string} tableType - '5_PLAYERS' or '9_PLAYERS'
 * @param {string} level - 'BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'PRO'
 * @returns {Object|null} Tournament configuration or null if not found
 */
SngTableService.prototype.findTournamentConfig = function(tableType, level) {
    var playerCapacity = tableType === '5_PLAYERS' ? 5 : (tableType === '9_PLAYERS' ? 9 : 3); // Added 3 for testing
    return sngTournaments.tournaments.find(function(t) {
        return t.player_capacity === playerCapacity && t.level === level;
    });
};

/**
 * Create tournament table instance for database tournament
 * @param {Object} tournament - Database tournament object
 * @param {Object} tournamentConfig - Config from JSON
 * @returns {Object} Table object
 */
SngTableService.prototype.createTournamentTable = function(tournament, tournamentConfig) {
    var self = this;
    var tableId = 'sng_' + tournament.id;
    
    logger.info("[createTournamentTable] Creating table for tournament:", tournament.id);
    
    // Create table object
    var tableObj = {
        id: tableId,
        tournament_id: tournament.id,
        creator: null, // Will be set when first player joins
        state: 'WAITING',
        tableService: self,
        db_tournament: tournament
    };
    
    // Create SNG table instance with initial blind level
    var initialBlind = sngTournaments.blind_structure[0];
    tableObj.table = new SngTable(
        initialBlind.small_blind,
        initialBlind.big_blind,
        2, // minPlayers
        tournamentConfig.player_capacity,
        tournamentConfig.initial_chips,
        tournamentConfig.initial_chips,
        'sng', // gameMode
        tableObj
    );
    
    // Set tournament-specific properties
    tableObj.table.tournament_id = tournament.id;
    tableObj.table.blind_structure = sngTournaments.blind_structure;
    tableObj.table.current_blind_level = 0;
    tableObj.table.payout_structure = sngTournaments.reward_distribution;
    tableObj.table.ante = initialBlind.ante || 0;
    
    // Initialize members array
    tableObj.table.members = [];
    
    // Store table
    self.tables[tableId] = tableObj;
    
    return tableObj;
};

