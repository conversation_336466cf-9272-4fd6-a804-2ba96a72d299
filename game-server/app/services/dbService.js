'use strict';

const logger = require('pomelo-logger').getLogger('db-service', __filename);
const { models, Sequelize, sequelize } = require("../models");
const CODE = require('../consts/code');
const UTILS = require('../util/utils');
const TransactionDTO = require('../domain/entity/TransactionDTO');
const {PlayerDTO} = require('../domain/entity/player');
const PropertyDTO = require('../domain/entity/property');
const ItemDTO = require('../domain/entity/ItemDTO');
const PlayerItemDTO = require('../domain/entity/PlayerItemDTO');
const ItemUsageDTO = require('../domain/entity/ItemUsageDTO');
// const { include } = require('underscore');

// Import models
const Transactions = models.Transactions;
const Player = models.Player;
const Properties = models.Properties;
const Leaderboard = models.Leaderboard;
const Items = models.Items;
const PlayerItems = models.PlayerItems;
const ItemUsages = models.ItemUsages;
const SngTournamentLog = models.SngTournamentLog;
const Op = Sequelize.Op;

/**
 * Database service for handling transactions
 * @constructor
 */
class DbService {
    constructor(app) {
        this.app = app;
        this.name = 'dbService';
    }

    /**
     * Create a new transaction record
     * @param {Object} transactionData - Transaction data
     * @param {Number} transactionData.player_id - Player ID
     * @param {Number} transactionData.amount - Amount of transaction (positive or negative)
     * @param {Number} transactionData.before_balance - Balance before transaction
     * @param {Number} transactionData.after_balance - Balance after transaction
     * @param {String} transactionData.type - Transaction type (game, login_bonus, event, mission, shop, etc.)
     * @param {String} transactionData.action - Action performed (win, lose, buy_item, daily_login, complete_mission, etc.)
     * @param {Number} transactionData.reference_id - ID of related record (optional)
     * @param {String} transactionData.reference_type - Type of related record (optional)
     * @param {Object} transactionData.meta - Additional data (optional)
     * @param {String} transactionData.description - Description (optional)
     * @param {Function} cb - Callback function
     */
    async createTransaction(transactionData, cb) {
        logger.info('[dbService.createTransaction] data:', JSON.stringify(transactionData));

        try {
            // Validate required fields
            if (!transactionData.player_id || transactionData.amount === undefined ||
                !transactionData.type || !transactionData.action) {
                return cb(null, CODE.BAD_REQUEST, 'Missing required fields');
            }

            // Create transaction record
            const transaction = await Transactions.create({
                player_id: transactionData.player_id,
                amount: transactionData.amount,
                before_balance: transactionData.before_balance,
                after_balance: transactionData.after_balance,
                type: transactionData.type,
                action: transactionData.action,
                reference_id: transactionData.reference_id,
                reference_type: transactionData.reference_type,
                meta: transactionData.meta || {},
                description: transactionData.description
            });

            // Convert to DTO
            const transactionDTO = new TransactionDTO(transaction);

            return cb(null, CODE.OK, transactionDTO);
        } catch (error) {
            logger.error('[dbService.createTransaction] Error:', error);
            return cb(null, CODE.FAIL, error.message);
        }
    }

    /**
     * Get transactions by player ID
     * @param {Object} params - Query parameters
     * @param {Number} params.player_id - Player ID
     * @param {String} params.type - Transaction type (optional)
     * @param {String} params.action - Transaction action (optional)
     * @param {Number} params.page - Page number (default: 1)
     * @param {Number} params.limit - Number of records per page (default: 20)
     * @param {Function} cb - Callback function
     */
    async getTransactionsByPlayerId(params, cb) {
        logger.info('[dbService.getTransactionsByPlayerId] params:', JSON.stringify(params));

        try {
            const player_id = params.player_id;
            const type = params.type;
            const action = params.action;
            const page = parseInt(params.page) || 1;
            const limit = parseInt(params.limit) || 20;
            const offset = (page - 1) * limit;

            // Build where clause
            const whereClause = { player_id };
            if (type) whereClause.type = type;
            if (action) whereClause.action = action;

            // Get transactions with pagination
            const { count, rows } = await Transactions.findAndCountAll({
                where: whereClause,
                order: [['created_at', 'DESC']],
                limit,
                offset,
                include: [{
                    model: Player,
                    as: 'player',
                    attributes: ['id', 'nick_name', 'avatar', 'display_name']
                }]
            });

            // Convert to DTOs
            const transactionDTOs = TransactionDTO.fromList(rows);

            // Prepare response with pagination info
            const response = {
                transactions: transactionDTOs,
                pagination: {
                    total: count,
                    page,
                    limit,
                    pages: Math.ceil(count / limit)
                }
            };

            return cb(null, CODE.OK, response);
        } catch (error) {
            logger.error('[dbService.getTransactionsByPlayerId] Error:', error);
            return cb(null, CODE.FAIL, error.message);
        }
    }

    /**
     * Get transaction by ID
     * @param {Number} id - Transaction ID
     * @param {Function} cb - Callback function
     */
    async getTransactionById(id, cb) {
        logger.info('[dbService.getTransactionById] id:', id);

        try {
            const transaction = await Transactions.findByPk(id, {
                include: [{
                    model: Player,
                    as: 'player',
                    attributes: ['id', 'nick_name', 'avatar', 'display_name']
                }]
            });

            if (!transaction) {
                return cb(null, CODE.NOT_FOUND, 'Transaction not found');
            }

            // Convert to DTO
            const transactionDTO = new TransactionDTO(transaction);

            return cb(null, CODE.OK, transactionDTO);
        } catch (error) {
            logger.error('[dbService.getTransactionById] Error:', error);
            return cb(null, CODE.FAIL, error.message);
        }
    }

    /**
     * Get transactions by reference
     * @param {Object} params - Query parameters
     * @param {Number} params.reference_id - Reference ID
     * @param {String} params.reference_type - Reference type
     * @param {Function} cb - Callback function
     */
    async getTransactionsByReference(params, cb) {
        logger.info('[dbService.getTransactionsByReference] params:', JSON.stringify(params));

        try {
            const reference_id = params.reference_id;
            const reference_type = params.reference_type;

            if (!reference_id || !reference_type) {
                return cb(null, CODE.BAD_REQUEST, 'Missing reference_id or reference_type');
            }

            const transactions = await Transactions.findAll({
                where: {
                    reference_id,
                    reference_type
                },
                order: [['created_at', 'DESC']],
                include: [{
                    model: Player,
                    as: 'player',
                    attributes: ['id', 'nick_name', 'avatar', 'display_name']
                }]
            });

            // Convert to DTOs
            const transactionDTOs = TransactionDTO.fromList(transactions);

            return cb(null, CODE.OK, transactionDTOs);
        } catch (error) {
            logger.error('[dbService.getTransactionsByReference] Error:', error);
            return cb(null, CODE.FAIL, error.message);
        }
    }

    /**
     * Record a transaction for a player
     * This is a helper method that combines getting the current balance and creating a transaction
     * @param {Object} data - Transaction data
     * @param {Number} data.player_id - Player ID
     * @param {Number} data.amount - Amount of transaction (positive or negative)
     * @param {String} data.type - Transaction type
     * @param {String} data.action - Action performed
     * @param {Number} data.reference_id - ID of related record (optional)
     * @param {String} data.reference_type - Type of related record (optional)
     * @param {Object} data.meta - Additional data (optional)
     * @param {String} data.description - Description (optional)
     * @param {Function} cb - Callback function
     */
    async recordTransaction(data, cb) {
        logger.info('[dbService.recordTransaction] data:', JSON.stringify(data));

        const transaction = await sequelize.transaction();

        try {
            // // Get current balance from coin service
            // const coinGrpcService = this.app.get('coinGrpcService');

            // // Get player balance
            // const balanceResponse = await new Promise((resolve, reject) => {
            //     coinGrpcService.callMethod('GetBalance', { user_id: data.player_id }, (err, response) => {
            //         if (err) {
            //             reject(err);
            //         } else {
            //             resolve(response);
            //         }
            //     });
            // });

            const { codePlayer, playerInfo } = await new Promise((resolve, reject) => {
                logger.info('[dbService.recordTransaction] player_id: ', data.player_id);
                this.app.rpc.db.dbRemote.getPlayerById('*', data.player_id, (err, codePlayer, playerInfo) => {
                    if (err) reject(err);
                    else resolve({ codePlayer, playerInfo });
                });
            });

            if (codePlayer !== CODE.OK) {
                await transaction.rollback();
                return cb(null, codePlayer, playerInfo);
            }

            const beforeBalance = parseInt(playerInfo.balance || 0);
            // const afterBalance = beforeBalance + parseInt(data.amount);
            const afterBalance = beforeBalance + data.amount;

            // Create transaction data
            const transactionData = {
                player_id: data.player_id,
                amount: data.amount,
                before_balance: beforeBalance,
                after_balance: afterBalance,
                type: data.type,
                action: data.action,
                reference_id: data.reference_id,
                reference_type: data.reference_type,
                meta: data.meta || {},
                description: data.description
            };

            // Create transaction record
            const transactionRecord = await Transactions.create(transactionData, { transaction });

            // Commit transaction
            await transaction.commit();

            // Convert to DTO
            const transactionDTO = new TransactionDTO(transactionRecord);

            return cb(null, CODE.OK, transactionDTO);
        } catch (error) {
            // Rollback transaction
            await transaction.rollback();

            logger.error('[dbService.recordTransaction] Error:', error, ' - error:', JSON.stringify(error), ' -> e: ',  error.message);
            return cb(null, CODE.FAIL, error.message);
        }
    }

    /**
     * Get transaction statistics for a player
     * @param {Number} player_id - Player ID
     * @param {Function} cb - Callback function
     */
    async getPlayerTransactionStats(player_id, cb) {
        logger.info('[dbService.getPlayerTransactionStats] player_id:', player_id);

        try {
            // Total transactions
            const totalTransactions = await Transactions.count({
                where: { player_id }
            });

            // Total income (positive transactions)
            const totalIncomeResult = await Transactions.sum('amount', {
                where: {
                    player_id,
                    amount: { [Op.gt]: 0 }
                }
            });
            const totalIncome = totalIncomeResult || 0;

            // Total expense (negative transactions)
            const totalExpenseResult = await Transactions.sum('amount', {
                where: {
                    player_id,
                    amount: { [Op.lt]: 0 }
                }
            });
            const totalExpense = totalExpenseResult || 0;

            // Transactions by type
            const transactionsByType = await Transactions.findAll({
                attributes: [
                    'type',
                    [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
                    [sequelize.fn('SUM', sequelize.col('amount')), 'total_amount']
                ],
                where: { player_id },
                group: ['type'],
                raw: true
            });

            // Prepare response
            const response = {
                player_id,
                total_transactions: totalTransactions,
                total_income: totalIncome,
                total_expense: totalExpense,
                net_balance: totalIncome + totalExpense,
                transactions_by_type: transactionsByType
            };

            return cb(null, CODE.OK, response);
        } catch (error) {
            logger.error('[dbService.getPlayerTransactionStats] Error:', error);
            return cb(null, CODE.FAIL, error.message);
        }
    }

    /**
     * Hàm lấy thông tin người chơi theo id
     * @param {Number} id id của người chơi
     * @param {Function} cb callback function
     * @returns 
     */
    async getPlayerById(id, cb) {
        logger.info('[dbService.getPlayerById] id:', id);

        try {
            const item = await Player.findByPk(id,  {
                include: [
                    {
                        model: Properties,
                        as: 'properties', // Giả sử bạn đã định nghĩa association với alias là 'property'
                        required: false // LEFT JOIN
                    }
                ],
                raw: false // Cần false để có thể truy cập các method của instance
            });

            if (!item) {
                return cb(null, CODE.NOT_FOUND, 'Player not found');
            }

            // Convert to DTO
            const itemDTO = new PlayerDTO(item);
            const properties = new PropertyDTO(item?.properties);
            itemDTO.properties = properties;

            logger.info('[dbService.getPlayerById] itemDTO: ', itemDTO);

            return cb(null, CODE.OK, itemDTO);
        } catch (error) {
            logger.error('[dbService.getPlayerById] Error:', error, ' -> message:', error.message, ' -> stack: ', error.stack);
            return cb(null, CODE.FAIL, error.message);
        }
    }

    async updatePlayer(player_id, data, cb) {
        logger.info('[dbService.updatePlayer] player_id:', player_id);
        logger.info('[dbService.updatePlayer] data:', data);
        try {
            const player = await Player.findByPk(player_id);
            if (!player) {
                return cb(null, CODE.FAIL, 'Player not found');
            }
            // Update player data
            await player.update(data);
            // Convert to DTO
            const playerDTO = new PlayerDTO(player);

            return cb(null, CODE.OK, playerDTO);
        } catch (error) {
            logger.error('[dbService.updatePlayer] Error:', error, ' -> message:', error.message, ' -> stack: ', error.stack);
            return cb(null, CODE.FAIL, error.message);
        }
    } // end of updatePlayer

    // hàm tính streak của player đăng nhập nhận thưởng liên tiếp, dựa vào dữ liệu trong table transactions, theo cột: type = 'login_bonus' and player_id = player_id và created_at nằm trong khoảng thời gian ngày hôm trước, nếu có thì tăng streak lên 1, nếu không có thì reset streak về 0
    async getPlayerStreakDailyLogin(player_id, cb) {
        logger.info('[dbService.getPlayerStreak] player_id:', player_id);
        try {
            const yesterday = UTILS.getYesterday(7, 'YYYY-MM-DD');
            // To match exactly yesterday's transactions
            const yesterdayStart = new Date(yesterday);
            yesterdayStart.setHours(0, 0, 1, 0);
            const yesterdayEnd = new Date(yesterday);
            yesterdayEnd.setHours(23, 59, 59, 999);

            const transactions = await Transactions.findAll({
                where: {
                    type: 'login_bonus',
                    player_id,
                    created_at: {
                        [Op.gte]: yesterdayStart,
                        [Op.lte]: yesterdayEnd
                    }
                },
                order: [['created_at', 'DESC']],
                limit: 1
            });

            if (transactions.length > 0) {
                logger.info('[dbService.getPlayerStreak] transactions: ', transactions);
                const meta = transactions[0].meta || {};
                logger.info('[dbService.getPlayerStreak] meta: ', meta);
                const streak = meta.streak || 0;
                logger.info('[dbService.getPlayerStreak] streak: ', streak);
                return cb(null, CODE.OK, streak);
            } else {
                return cb(null, CODE.OK, 0);
            }
        } catch (error) {
            logger.error('[dbService.getPlayerStreak] Error:', error, ' -> message:', error.message, ' -> stack: ', error.stack);
            return cb(null, CODE.FAIL, error.message);
        }
    }

    /**
     * Get leaderboards by type and scope
     * @param {Object} params - Query parameters
     * @param {String} params.type - Leaderboard type (CHIPS, LEVEL, XP, RANK)
     * @param {String} params.scope - Leaderboard scope (GLOBAL, FRIENDS)
     * @param {String} params.season - Season (optional, defaults to current month)
     * @param {Number} params.limit - Number of records to return (default: 100)
     * @param {Number} params.player_id - Player ID to get friends leaderboard (required for FRIENDS scope)
     * @param {Function} cb - Callback function
     */
    async getLeaderboards(params, cb) {
        logger.info('[dbService.getLeaderboards] params:', JSON.stringify(params));

        try {
            const type = params.type;
            const scope = params.scope || 'GLOBAL';
            const limit = parseInt(params.limit) || 30;
            const player_id = params.player_id;

            // Default to current month if season not provided
            const season = params.season || UTILS.getCurrentWeek();

            // Validate required fields
            if (!type) {
                return cb(null, CODE.BAD_REQUEST, 'Missing leaderboard type');
            }

            // For FRIENDS scope, player_id is required
            if (scope === 'FRIENDS' && !player_id) {
                return cb(null, CODE.BAD_REQUEST, 'Player ID is required for FRIENDS scope');
            }

            // Build where clause
            const whereClause = {
                leaderboard_type: type,
                scope: scope,
                season: season
            };

            // For FRIENDS scope, we need to get the player's friends
            let playerIds = [];
            if (scope === 'FRIENDS') {
                // Get player's friends from the database
                // const friendsResult = await new Promise((resolve, reject) => {
                //     this.app.rpc.db.dbRemote.getFriendsByTypes('*', {uid: player_id, types: [1, 2], limit: limit}, (err, code, friends) => {
                //         if (err) reject(err);
                //         else resolve({ code, friends });
                //     });
                // });
                // logger.info('[dbService.getLeaderboards] friendsResult: ', friendsResult);
                // if (friendsResult.code !== CODE.OK) {
                //     return cb(null, friendsResult.code, 'Failed to get friends list');
                // }

                // // Extract friend IDs
                // playerIds = friendsResult.friends.friends.map(friend => friend.id);

                // // Add the player's own ID
                // playerIds.push(player_id);

                // // Update where clause to include only friends
                // whereClause.player_id = { [Op.in]: playerIds };
                whereClause.player_id = player_id;
            }

            // Get leaderboard entries
            const leaderboardEntries = await Leaderboard.findAll({
                where: whereClause,
                order: [['rank', 'ASC']],
                limit: limit,
                include: [{
                    model: Player,
                    as: 'player',
                    attributes: ['id', 'display_name', 'avatar']
                }]
            });

            // Format the response
            const formattedEntries = leaderboardEntries.map(entry => ({
                player_id: entry.player_id,
                display_name: entry.player ? entry.player.display_name : null,
                avatar: entry.player ? entry.player.avatar : null,
                rank: entry.rank,
                previous_rank: entry.previous_rank,
                rank_change_status: entry.rank_change_status,
                value: entry.value
            }));

            // Group by scope
            const response = {
                [scope]: formattedEntries
            };

            return cb(null, CODE.OK, response);
        } catch (error) {
            logger.error('[dbService.getLeaderboards] Error:', error, ' -> meessage:', error.message, ' -> stack: ', error.stack);;
            return cb(null, CODE.FAIL, error.message);
        }
    }

    /**
     * Update leaderboard entries
     * This method is typically called by a scheduled job to update leaderboards
     * @param {Object} params - Parameters
     * @param {String} params.type - Leaderboard type (CHIPS, LEVEL, XP, RANK)
     * @param {String} params.scope - Leaderboard scope (GLOBAL, FRIENDS)
     * @param {String} params.season - Season (optional, defaults to current month)
     * @param {Function} cb - Callback function
     */
    async updateLeaderboards(params, cb) {
        logger.info('[dbService.updateLeaderboards] params:', JSON.stringify(params));

        const transaction = await sequelize.transaction();

        try {
            const type = params.type;
            const scope = params.scope || 'GLOBAL';

            // Default to current month if season not provided
            const season = params.season || UTILS.getCurrentDateTime(7, 'YYYY-MM');

            // Validate required fields
            if (!type) {
                await transaction.rollback();
                return cb(null, CODE.BAD_REQUEST, 'Missing leaderboard type');
            }

            // Get current leaderboard entries to save previous ranks
            const currentEntries = await Leaderboard.findAll({
                where: {
                    leaderboard_type: type,
                    scope: scope,
                    season: season
                },
                attributes: ['player_id', 'rank'],
                raw: true
            });

            // Create a map of player_id to rank for quick lookup
            const playerRankMap = {};
            currentEntries.forEach(entry => {
                playerRankMap[entry.player_id] = entry.rank;
            });

            // Get player data based on leaderboard type
            let players;
            switch (type) {
                case 'CHIPS':
                    // Get players sorted by balance
                    players = await Player.findAll({
                        attributes: ['id', 'balance'],
                        order: [['balance', 'DESC']],
                        raw: true
                    });
                    break;
                case 'LEVEL':
                    // Get players sorted by level
                    players = await Player.findAll({
                        attributes: ['id', 'level'],
                        order: [['level', 'DESC']],
                        raw: true
                    });
                    break;
                case 'XP':
                    // Get players sorted by exp
                    players = await Player.findAll({
                        attributes: ['id', 'exp'],
                        order: [['exp', 'DESC']],
                        raw: true
                    });
                    break;
                case 'RANK':
                    // Get players sorted by rank
                    players = await Player.findAll({
                        attributes: ['id', 'rank'],
                        order: [['rank', 'DESC']],
                        raw: true
                    });
                    break;
                default:
                    await transaction.rollback();
                    return cb(null, CODE.BAD_REQUEST, 'Invalid leaderboard type');
            }

            // Prepare leaderboard entries
            const leaderboardEntries = [];
            players.forEach((player, index) => {
                const valueField = type.toLowerCase();
                leaderboardEntries.push({
                    player_id: player.id,
                    leaderboard_type: type,
                    scope: scope,
                    season: season,
                    rank: index + 1,
                    previous_rank: playerRankMap[player.id] || null,
                    value: player[valueField] || 0
                });
            });

            // Delete existing entries for this type, scope, and season
            await Leaderboard.destroy({
                where: {
                    leaderboard_type: type,
                    scope: scope,
                    season: season
                },
                transaction
            });

            // Insert new entries
            await Leaderboard.bulkCreate(leaderboardEntries, { transaction });

            // Commit transaction
            await transaction.commit();

            return cb(null, CODE.OK, { message: 'Leaderboards updated successfully' });
        } catch (error) {
            // Rollback transaction
            await transaction.rollback();

            logger.error('[dbService.updateLeaderboards] Error:', error, ' -> message:', error.message, ' -> stack:', error.stack);
            return cb(null, CODE.FAIL, error.message);
        }
    }

    /**
     * Get player ranking in leaderboard
     * @param {Object} params - Parameters
     * @param {Number} params.player_id - Player ID
     * @param {String} params.type - Leaderboard type (CHIPS, LEVEL, XP, RANK)
     * @param {String} params.scope - Leaderboard scope (GLOBAL, FRIENDS)
     * @param {String} params.season - Season (optional, defaults to current month)
     * @param {Function} cb - Callback function
     */
    async getPlayerRanking(params, cb) {
        logger.info('[dbService.getPlayerRanking] params:', JSON.stringify(params));

        try {
            const player_id = params.player_id;
            const type = params.type;
            const scope = params.scope || 'GLOBAL';

            // Default to current month if season not provided
            const season = params.season || UTILS.getCurrentDateTime(7, 'YYYY-MM');

            // Validate required fields
            if (!player_id || !type) {
                return cb(null, CODE.BAD_REQUEST, 'Missing player_id or leaderboard type');
            }

            // Get player's ranking
            const playerRanking = await Leaderboard.findOne({
                where: {
                    player_id: player_id,
                    leaderboard_type: type,
                    scope: scope,
                    season: season
                },
                include: [{
                    model: Player,
                    as: 'player',
                    attributes: ['id', 'display_name', 'avatar']
                }]
            });

            if (!playerRanking) {
                return cb(null, CODE.NOT_FOUND, 'Player ranking not found');
            }

            // Format the response
            const response = {
                player_id: playerRanking.player_id,
                display_name: playerRanking.player ? playerRanking.player.display_name : null,
                avatar: playerRanking.player ? playerRanking.player.avatar : null,
                rank: playerRanking.rank,
                previous_rank: playerRanking.previous_rank,
                rank_change_status: playerRanking.rank_change_status,
                value: playerRanking.value,
                type: playerRanking.leaderboard_type,
                scope: playerRanking.scope,
                season: playerRanking.season
            };

            return cb(null, CODE.OK, response);
        } catch (error) {
            logger.error('[dbService.getPlayerRanking] Error:', error, ' -> message:', error.message, ' -> stack:', error.stack);
            return cb(null, CODE.FAIL, error.message);
        }
    }

    /**
     * Get item by ID
     * @param {Number} id - Item ID
     * @param {Function} cb - Callback function
     */
    async getItemById(id, cb) {
        logger.info('[dbService.getItemById] id:', id);

        try {
            const item = await Items.findByPk(id);

            if (!item) {
                return cb(null, CODE.NOT_FOUND, 'Item not found');
            }

            // Convert to DTO
            const itemDTO = new ItemDTO(item);

            return cb(null, CODE.OK, itemDTO);
        } catch (error) {
            logger.error('[dbService.getItemById] Error:', error);
            return cb(null, CODE.FAIL, error.message);
        }
    }

    /**
     * Get item by code
     * @param {String} code - Item code
     * @param {Function} cb - Callback function
     */
    async getItemByCode(code, cb) {
        logger.info('[dbService.getItemByCode] code:', code);

        try {
            const item = await Items.findOne({
                where: {
                    code: code
                }
            });

            if (!item) {
                return cb(null, CODE.NOT_FOUND, 'Item not found');
            }

            // Convert to DTO
            const itemDTO = new ItemDTO(item);

            return cb(null, CODE.OK, itemDTO);
        } catch (error) {
            logger.error('[dbService.getItemByCode] Error:', error, ' -> message:', error.message, ' -> stack:', error.stack);
            return cb(null, CODE.FAIL, error.message);
        }
    }


    /**
     * Get player item by player_id and item_id
     * @param {Number} player_id - Player ID
     * @param {Number} item_id - Item ID
     * @param {Function} cb - Callback function
     */
    async getPlayerItemByPlayerIdAndItemId(player_id, item_id, cb) {
        logger.info('[dbService.getPlayerItemByPlayerIdAndItemId] player_id:', player_id, 'item_id:', item_id);

        try {
            const item = await PlayerItems.findOne({
                where: {
                    player_id: player_id,
                    item_id: item_id
                },
                include: [{
                    model: Items,
                    as: 'item'
                }]
            });

            if (!item) {
                return cb(null, CODE.NOT_FOUND, 'Item not found');
            }

            // Convert to DTO
            const itemDTO = new ItemDTO(item);

            return cb(null, CODE.OK, itemDTO);
        } catch (error) {
            logger.error('[dbService.getPlayerItemByPlayerIdAndItemId] Error:', error, ' -> message:', error.message, ' -> stack:', error.stack);
            return cb(null, CODE.FAIL, error.message);
        }
    }

    /**
     * Get all items with optional filtering
     * @param {Object} params - Query parameters
     * @param {String} params.type - Filter by item type (optional)
     * @param {Boolean} params.is_consumable - Filter by consumable status (optional)
     * @param {Number} params.page - Page number (default: 1)
     * @param {Number} params.limit - Number of records per page (default: 20)
     * @param {Function} cb - Callback function
     */
    async getItems(params, cb) {
        logger.info('[dbService.getItems] params:', JSON.stringify(params));

        try {
            const type = params.type;
            const isConsumable = params.is_consumable !== undefined ? 
                Boolean(params.is_consumable) : undefined;
            const page = parseInt(params.page) || 1;
            const limit = parseInt(params.limit) || 20;
            const offset = (page - 1) * limit;

            // Build where clause
            const whereClause = {};
            if (type) whereClause.type = type;
            if (isConsumable !== undefined) whereClause.is_consumable = isConsumable;

            // Get items with pagination
            const { count, rows } = await Items.findAndCountAll({
                where: whereClause,
                order: [['created_at', 'DESC']],
                limit,
                offset
            });

            // Convert to DTOs
            const itemDTOs = ItemDTO.fromList(rows);

            // Prepare response with pagination info
            const response = {
                items: itemDTOs,
                pagination: {
                    total: count,
                    page,
                    limit,
                    pages: Math.ceil(count / limit)
                }
            };

            return cb(null, CODE.OK, response);
        } catch (error) {
            logger.error('[dbService.getItems] Error:', error);
            return cb(null, CODE.FAIL, error.message);
        }
    }

    /**
     * Add a new item to the database
     * @param {Object} itemData - Item data to add
     * @param {Function} cb - Callback function
     */
    async addItem(itemData, cb) {
        logger.info('[dbService.addItem] data:', JSON.stringify(itemData));

        try {
            // Validate required fields
            if (!itemData.name || !itemData.type) {
                return cb(null, CODE.BAD_REQUEST, 'Missing required fields');
            }

            // Create item record
            const item = await Items.create({
                name: itemData.name,
                description: itemData.description,
                type: itemData.type,
                price: itemData.price || 0,
                is_consumable: itemData.is_consumable !== undefined ? 
                    Boolean(itemData.is_consumable) : true,
                metadata: itemData.metadata || {}
            });

            // Convert to DTO
            const itemDTO = new ItemDTO(item);

            return cb(null, CODE.OK, itemDTO);
        } catch (error) {
            logger.error('[dbService.addItem] Error:', error);
            return cb(null, CODE.FAIL, error.message);
        }
    }

    /**
     * Update an existing item
     * @param {Number} id - Item ID
     * @param {Object} itemData - Updated item data
     * @param {Function} cb - Callback function
     */
    async updateItem(id, itemData, cb) {
        logger.info('[dbService.updateItem] id:', id, 'data:', JSON.stringify(itemData));

        try {
            const item = await Items.findByPk(id);

            if (!item) {
                return cb(null, CODE.NOT_FOUND, 'Item not found');
            }

            // Update item fields
            await item.update({
                name: itemData.name !== undefined ? itemData.name : item.name,
                description: itemData.description !== undefined ? itemData.description : item.description,
                type: itemData.type !== undefined ? itemData.type : item.type,
                price: itemData.price !== undefined ? itemData.price : item.price,
                is_consumable: itemData.is_consumable !== undefined ? 
                    Boolean(itemData.is_consumable) : item.is_consumable,
                metadata: itemData.metadata !== undefined ? itemData.metadata : item.metadata
            });

            // Convert updated item to DTO
            const itemDTO = new ItemDTO(item);

            return cb(null, CODE.OK, itemDTO);
        } catch (error) {
            logger.error('[dbService.updateItem] Error:', error);
            return cb(null, CODE.FAIL, error.message);
        }
    }

    /**
     * Delete an item
     * @param {Number} id - Item ID
     * @param {Function} cb - Callback function
     */
    async deleteItem(id, cb) {
        logger.info('[dbService.deleteItem] id:', id);

        try {
            const item = await Items.findByPk(id);

            if (!item) {
                return cb(null, CODE.NOT_FOUND, 'Item not found');
            }

            // Check if item is in use by any player
            const playerItemCount = await PlayerItems.count({
                where: { item_id: id }
            });

            if (playerItemCount > 0) {
                return cb(null, CODE.BAD_REQUEST, 'Cannot delete item that is owned by players');
            }

            // Delete the item
            await item.destroy();

            return cb(null, CODE.OK, { message: 'Item deleted successfully' });
        } catch (error) {
            logger.error('[dbService.deleteItem] Error:', error);
            return cb(null, CODE.FAIL, error.message);
        }
    }

    /**
     * Get player items by player ID
     * @param {Object} params - Query parameters
     * @param {Number} params.player_id - Player ID
     * @param {Number} params.item_id - Filter by item ID (optional)
     * @param {Boolean} params.used - Filter by used status (optional)
     * @param {Function} cb - Callback function
     */
    async getPlayerItems(params, cb) {
        logger.info('[dbService.getPlayerItems] params:', JSON.stringify(params));

        try {
            const playerId = params.player_id;
            const itemId = params.item_id;
            const used = params.used !== undefined ? Boolean(params.used) : undefined;

            if (!playerId) {
                return cb(null, CODE.BAD_REQUEST, 'Missing player_id');
            }

            // Build where clause
            const whereClause = { player_id: playerId };
            if (itemId) whereClause.item_id = itemId;
            if (used !== undefined) whereClause.used = used;

            // Get player items
            const playerItems = await PlayerItems.findAll({
                where: whereClause,
                include: [
                    {
                        model: Items,
                        as: 'item'
                    },
                    {
                        model: Player,
                        as: 'player',
                        attributes: ['id', 'nick_name', 'avatar', 'display_name']
                    }
                ],
                order: [['acquired_at', 'DESC']]
            });

            // Convert to DTOs
            const playerItemDTOs = PlayerItemDTO.fromList(playerItems);

            return cb(null, CODE.OK, playerItemDTOs);
        } catch (error) {
            logger.error('[dbService.getPlayerItems] Error:', error);
            return cb(null, CODE.FAIL, error.message);
        }
    }

    /**
     * Give an item to a player (tặng vật phẩm cho người chơi)
     * @param {Object} data - Data for giving item
     * @param {Number} data.player_id - Player ID
     * @param {Number} data.item_id - Item ID
     * @param {Number} data.quantity - Quantity (default: 1)
     * @param {Date} data.expires_at - Expiry date (optional)
     * @param {Function} cb - Callback function
     */
    async giveItemToPlayer(data, cb) {
        logger.info('[dbService.giveItemToPlayer] data:', JSON.stringify(data));

        try {
            const playerId = data.player_id;
            const itemId = data.item_id;
            const quantity = data.quantity || 1;
            const expiresAt = data.expires_at || null;

            if (!playerId || !itemId) {
                return cb(null, CODE.BAD_REQUEST, 'Missing player_id or item_id');
            }

            // Check if player exists
            const player = await Player.findByPk(playerId);
            if (!player) {
                return cb(null, CODE.NOT_FOUND, 'Player not found');
            }

            // Check if item exists
            const item = await Items.findByPk(itemId);
            if (!item) {
                return cb(null, CODE.NOT_FOUND, 'Item not found');
            }

            // If item is non-consumable, check if player already has it
            if (!item.is_consumable) {
                const existingItem = await PlayerItems.findOne({
                    where: {
                        player_id: playerId,
                        item_id: itemId
                    }
                });

                if (existingItem) {
                    // Increment quantity if already exists
                    await existingItem.update({
                        quantity: existingItem.quantity + quantity
                    });

                    // Get the updated player item with associations
                    const updatedPlayerItem = await PlayerItems.findOne({
                        where: { id: existingItem.id },
                        include: [
                            { model: Items, as: 'item' },
                            { 
                                model: Player, 
                                as: 'player',
                                attributes: ['id', 'nick_name', 'avatar', 'display_name']
                            }
                        ]
                    });

                    return cb(null, CODE.OK, new PlayerItemDTO(updatedPlayerItem));
                }
            }

            // Create new player item
            const playerItem = await PlayerItems.create({
                player_id: playerId,
                item_id: itemId,
                quantity: quantity,
                expires_at: expiresAt,
                used: false
            });

            // Get the player item with associations
            const createdPlayerItem = await PlayerItems.findOne({
                where: { id: playerItem.id },
                include: [
                    { model: Items, as: 'item' },
                    { 
                        model: Player, 
                        as: 'player',
                        attributes: ['id', 'nick_name', 'avatar', 'display_name']
                    }
                ]
            });

            return cb(null, CODE.OK, new PlayerItemDTO(createdPlayerItem));
        } catch (error) {
            logger.error('[dbService.giveItemToPlayer] Error:', error);
            return cb(null, CODE.FAIL, error.message);
        }
    }

    /**
     * Use an item
     * @param {Object} data - Data for using an item
     * @param {Number} data.player_id - Player ID
     * @param {Number} data.player_item_id - Player Item ID
     * @param {String} data.context - Context of usage (optional)
     * @param {Function} cb - Callback function
     */
    async useItem(data, cb) {
        logger.info('[dbService.useItem] data:', JSON.stringify(data));

        try {
            const playerId = data.player_id;
            const playerItemId = data.player_item_id;
            const context = data.context || '';

            if (!playerId || !playerItemId) {
                return cb(null, CODE.BAD_REQUEST, 'Missing player_id or player_item_id');
            }

            // Get player item
            const playerItem = await PlayerItems.findOne({
                where: {
                    id: playerItemId,
                    player_id: playerId
                },
                include: [
                    { model: Items, as: 'item' }
                ]
            });

            if (!playerItem) {
                return cb(null, CODE.NOT_FOUND, 'Player item not found');
            }

            // Check if item is already used (for consumables)
            if (playerItem.used && playerItem.item.is_consumable) {
                return cb(null, CODE.BAD_REQUEST, 'Item has already been used');
            }

            // Check if item is expired
            if (playerItem.expires_at && new Date(playerItem.expires_at) < new Date()) {
                return cb(null, CODE.BAD_REQUEST, 'Item has expired');
            }

            // If is_consumable = false (cho sử dụng nhiều lần)
            if (!playerItem.item.is_consumable) {
                await playerItem.update({
                    // used: true,
                    quantity: playerItem.quantity > 1 ? playerItem.quantity - 1 : 0
                });
            }

            // nếu is_consumable = true (cho sử dụng 1 lần), thì set trạng thái used = true
            if (playerItem.item.is_consumable) {
                await playerItem.update({
                    used: true,
                    quantity: playerItem.quantity > 1 ? playerItem.quantity - 1 : 0
                });
            }

            // Record usage in item_usages table
            const itemUsage = await ItemUsages.create({
                player_id: playerId,
                item_id: playerItem.item_id,
                context: context
            });

            // Get the item usage with associations
            const createdItemUsage = await ItemUsages.findOne({
                where: { id: itemUsage.id },
                include: [
                    { model: Items, as: 'item' },
                    { 
                        model: Player, 
                        as: 'player',
                        attributes: ['id', 'nick_name', 'avatar', 'display_name']
                    }
                ]
            });

            return cb(null, CODE.OK, new ItemUsageDTO(createdItemUsage));
        } catch (error) {
            logger.error('[dbService.useItem] Error:', error);
            return cb(null, CODE.FAIL, error.message);
        }
    }

    /**
     * Get player item usage history
     * @param {Object} params - Query parameters
     * @param {Number} params.player_id - Player ID
     * @param {Number} params.item_id - Filter by item ID (optional)
     * @param {Number} params.page - Page number (default: 1)
     * @param {Number} params.limit - Number of records per page (default: 20)
     * @param {Function} cb - Callback function
     */
    async getItemUsageHistory(params, cb) {
        logger.info('[dbService.getItemUsageHistory] params:', JSON.stringify(params));

        try {
            const playerId = params.player_id;
            const itemId = params.item_id;
            const page = parseInt(params.page) || 1;
            const limit = parseInt(params.limit) || 20;
            const offset = (page - 1) * limit;

            if (!playerId) {
                return cb(null, CODE.BAD_REQUEST, 'Missing player_id');
            }

            // Build where clause
            const whereClause = { player_id: playerId };
            if (itemId) whereClause.item_id = itemId;

            // Get item usages with pagination
            const { count, rows } = await ItemUsages.findAndCountAll({
                where: whereClause,
                include: [
                    { model: Items, as: 'item' },
                    { 
                        model: Player, 
                        as: 'player',
                        attributes: ['id', 'nick_name', 'avatar', 'display_name']
                    }
                ],
                order: [['used_at', 'DESC']],
                limit,
                offset
            });

            // Convert to DTOs
            const itemUsageDTOs = ItemUsageDTO.fromList(rows);

            // Prepare response with pagination info
            const response = {
                usages: itemUsageDTOs,
                pagination: {
                    total: count,
                    page,
                    limit,
                    pages: Math.ceil(count / limit)
                }
            };

            return cb(null, CODE.OK, response);
        } catch (error) {
            logger.error('[dbService.getItemUsageHistory] Error:', error);
            return cb(null, CODE.FAIL, error.message);
        }
    }

    /**
     * Tạo tournament trong database
     * @param {Object} payload - Dữ liệu tournament
     * @param {Function} cb - Callback function
     */
    async createSngTournament(payload, cb) {
        logger.info('[dbService.createSngTournament] payload:', payload);
    
        try {
            const tournament = await models.SngTournament.create(payload);
    
            logger.info('[dbService.createSngTournament] Tournament created successfully:', tournament.id);
            return cb(null, CODE.OK, tournament);
        } catch (error) {
            logger.error('[dbService.createSngTournament] Error:', error, ' -> message:', error.message, ' -> stack:', error.stack);
            return cb(null, CODE.FAIL, error.message);
        }
    }

    /**
     * Ghi log hành động trong giải đấu SNG
     * @param {Object} payload - Dữ liệu log
     * @param {Function} cb - Callback function
     */
    async createSngTournamentLog(payload, cb) {
        logger.info('[dbService.createSngTournamentLog] payload:', payload);
    
        try {
            const { tournament_id, player_id, action_type, data, amount } = payload;
    
            const log = await SngTournamentLog.create({
                tournament_id: tournament_id,
                player_id: player_id,
                action_type: action_type,
                data: data,
                amount: amount
            });
    
            logger.info('[dbService.createSngTournamentLog] Log created successfully:', log.id);
            return cb(null, CODE.OK, log);
        } catch (error) {
            logger.error('[dbService.createSngTournamentLog] Error:', error, ' -> message:', error.message, ' -> stack:', error.stack);
            return cb(null, CODE.FAIL, error.message);
        }
    }

    /**
     * Tạo tournament player trong database
     * @param {Object} payload - Dữ liệu tournament player
     * @param {Function} cb - Callback function
     */
    async createSngTournamentPlayer(payload, cb) {
        logger.info('[dbService.createSngTournamentPlayer] payload:', payload);
    
        try {
            const tournamentPlayer = await models.SngTournamentPlayer.create(payload);
    
            logger.info('[dbService.createSngTournamentPlayer] Tournament player created successfully:', tournamentPlayer.id);
            return cb(null, CODE.OK, tournamentPlayer);
        } catch (error) {
            logger.error('[dbService.createSngTournamentPlayer] Error:', error, ' -> message:', error.message, ' -> stack:', error.stack);
            return cb(null, CODE.FAIL, error.message);
        }
    }

    /**
     * Cập nhật trạng thái giải đấu SNG
     * @param {Object} payload - Dữ liệu cập nhật
     * @param {Function} cb - Callback function
     */
    async updateSngTournament(payload, cb) {
        logger.info('[dbService.updateSngTournament] payload:', payload);
    
        try {
            const { tournament_id, status, started_at, ended_at } = payload;
    
            const updateData = { status };
            if (started_at) updateData.started_at = started_at;
            if (ended_at) updateData.ended_at = ended_at;
    
            const [affectedRows] = await models.SngTournament.update(updateData, {
                where: { id: tournament_id }
            });
    
            if (affectedRows > 0) {
                logger.info('[dbService.updateSngTournament] Tournament updated successfully');
                return cb(null, CODE.OK, { affected_rows: affectedRows });
            } else {
                logger.warn('[dbService.updateSngTournament] No tournament found to update');
                return cb(null, CODE.NOT_FOUND, null);
            }
        } catch (error) {
            logger.error('[dbService.updateSngTournament] Error:', error, ' -> message:', error.message, ' -> stack:', error.stack);
            return cb(null, CODE.FAIL, error.message);
        }
    }

    /**
     * Cập nhật trạng thái người chơi trong giải đấu SNG
     * @param {Object} payload - Dữ liệu cập nhật
     * @param {Function} cb - Callback function
     */
    async updateSngTournamentPlayer(payload, cb) {
        logger.info('[dbService.updateSngTournamentPlayer] payload:', payload);
    
        try {
            const { tournament_id, player_id, status, rank, current_chips, eliminated_at_hand } = payload;
    
            const [affectedRows] = await models.SngTournamentPlayer.update({
                status: status,
                rank: rank,
                current_chips: current_chips,
                eliminated_at_hand: eliminated_at_hand
            }, {
                where: {
                    tournament_id: tournament_id,
                    player_id: player_id
                }
            });
    
            if (affectedRows > 0) {
                logger.info('[dbService.updateSngTournamentPlayer] Player updated successfully');
                return cb(null, CODE.OK, { affected_rows: affectedRows });
            } else {
                logger.warn('[dbService.updateSngTournamentPlayer] No player found to update');
                return cb(null, CODE.NOT_FOUND, null);
            }
        } catch (error) {
            logger.error('[dbService.updateSngTournamentPlayer] Error:', error, ' -> message:', error.message, ' -> stack:', error.stack);
            return cb(null, CODE.FAIL, error.message);
        }
    }
}

module.exports = DbService;
