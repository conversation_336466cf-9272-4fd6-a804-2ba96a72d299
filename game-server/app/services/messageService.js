var pomelo      = require('pomelo');
//var logger    = require('pomelo-logger').getLogger(__filename);
var logger      = require('pomelo-logger').getLogger('message-service-log', __filename);
var dispatcher  = require('../util/dispatcher');

var exp = module.exports;

exp.pushMessageByUids = function (uids, route, msg) {
    logger.info("pushMessageByUids: uid: ", uids, " router: ", route, " msg: ", msg);
    pomelo.app.get('channelService').pushMessageByUids(route, msg, uids, errHandler);
};

exp.pushMessageToPlayer = function (uid, route, msg) {
    exp.pushMessageByUids([uid], route, msg);
};

/**
 * example
 * - messageService.pushMessageByUid(uid, "close", "{msg: Bạn bị kick khỏi game", type: "KICK_USER"});
 * @param uid
 * @param route
 * @param msg
 */
exp.pushMessageByUid = async function(uid, route, msg, code) {
    logger.info(`[pushMessageByUid][Step 1] Preparing to send message to user ${uid}, route: ${route}, code: ${code}`);

    if (!uid) {
        // const error = new Error('Invalid user ID');
        logger.error('[messageService.pushMessageByUid] Error: Invalid user ID');
        // throw error;
        return false;
    }

    try {
        // Get the server ID for the user
        const sid = await getSidByUid(uid, pomelo.app);

        if (!sid) {
            const error = new Error(`No server ID found for user ${uid}`);
            logger.error(`[messageService.pushMessageByUid] Error: ${error.message}`);
            // throw error;
            return false;
        }

        const receiver = { uid: uid, sid: sid };
        const msgArr = { msg: msg, type: route, code: code };

        logger.info(`[pushMessageByUid][Step 2] Sending message to user ${uid} on server ${sid}`);

        // Send the message
        pomelo.app.get('channelService').pushMessageByUids(route, msgArr, [receiver], function(err, fails) {
            if (err) {
                logger.error(`[pushMessageByUid] Error sending message: ${err.message || err}`);
            } else if (fails && fails.length > 0) {
                logger.error(`[pushMessageByUid] Failed to send message to some users: ${JSON.stringify(fails)}`);
            } else {
                logger.info(`[pushMessageByUid] Successfully sent message to user ${uid}`);
            }
        });

        return true;
    } catch (err) {
        logger.error(`[messageService.pushMessageByUid] Error: ${err.message || err}, uid: ${uid}`);
        // throw err;
        return false;
    }
};


function errHandler(err, fails){
    if (err) {
        logger.error(`Push Message error: ${err.message || err}`);
        if (err.stack) {
            logger.error(`Stack trace: ${err.stack}`);
        }
    }

    if (fails && fails.length > 0) {
        logger.error(`Failed to send message to users: ${JSON.stringify(fails)}`);
    }
}

// var getSidByUid = function(uid, app) {
//     var connector = dispatcher.dispatch(uid, app.getServersByType('connector'));
//     if(connector) {
//         return connector.id;
//     }
//     return null;
// };

// var getSidByUid = async function(uid, app) {
//     const sid = await new Promise((resolve, reject) => {
//         app.rpc.manager.userRemote.getUserCacheByUid('*', uid, function (user) {
//             logger.info("[getSidByUid] getSidByUid check uid: ", uid, ", user: ", user);
//             if (user) {
//                 const sidOfUser = user?.serverId ?? null;
//                 logger.info("[getSidByUid] getSidByUid return sid: ", sidOfUser, " with uid: ", uid);
//                 resolve(sidOfUser);
//             } else {
//                 logger.error("[getSidByUid] getSidByUid return null with uid: ", uid);
//                 reject(null);
//             }
//         });
//     });
//     return sid;
// };

var getSidByUid = async function(uid, app) {
    try {
        const sid = await new Promise((resolve, reject) => {
            app.rpc.manager.userRemote.getUserCacheByUid('*', uid, function (user) {
                if (user && user.serverId) {
                    const sidOfUser = user.serverId;
                    logger.info(`[messageService.getSidByUid] Found server ID ${sidOfUser} for user ${uid}`);
                    resolve(sidOfUser);
                } else {
                    logger.warn(`[messageService.getSidByUid] User ${uid} not found or not online`);
                    reject(new Error(`User ${uid} not found or not online`));
                }
            });
        });
        return sid;
    } catch (err) {
        logger.error(`[messageService.getSidByUid] Error getting server ID for user ${uid}: ${err.message || err}`);
        // throw err;
        return null;
    }
};
