/**
 * Module dependencies
 */
// const { Sequelize, DataTypes }  = require('sequelize');
var logger                      = require('pomelo-logger').getLogger('entry-log', __filename);
var pomelo                      = require('pomelo');
var UTILS                       = require('../util/utils');
var DEF                         = require('../consts/consts');
var Code                        = require('../consts/code');
const LogTypes                  = require('../consts/logTypes');
const LogBoards                 = require('../consts/logBoards');
const { models, Sequelize, sequelize } = require("./../models");
var _                           = require('underscore');
var Users                       = require('../dao/model/users');
const { PlayerDTO, PlayerLiteDTO }             = require('../domain/entity/player');
const PropertyDTO               = require('../domain/entity/property');
const { FriendDTO }             = require('../domain/entity/friend');
const MiniLuckyCardDTO          = require('../domain/entity/miniLuckyCard');
const MiniSpinHistoryDTO        = require('../domain/entity/miniSpinHistory');
const Op                        = Sequelize.Op;
const QueryTypes                = Sequelize.QueryTypes;
const Player                    = models.Player;
const Properties                = models.Properties;
const LogsGame                  = models.LogsGame;
const LidUids                   = models.LidUids;
const Friends                   = models.Friends;
const MiniLuckyCards            = models.MiniLuckyCards;
const MiniSpinHistory           = models.MiniSpinHistory;
const ChatSessions              = models.ChatSessions;
const LevelRank                 = models.LevelRank;
const ChatMessages              = models.ChatMessages;
const ShopItems                 = models.ShopItems;
const ShopPurchaseLogs          = models.ShopPurchaseLogs;
const winnerUtils               = require('../util/winnerUtils');
// Import DTOs
const ShopItemDTO               = require('../domain/entity/ShopItemDTO');
const ShopPurchaseLogDTO        = require('../domain/entity/ShopPurchaseLogDTO');
const DbService = require('../services/dbService');

// const sequelize                 = require('../models');
// var pomelo = require('pomelo');

var exp = module.exports;

//---------------------------------- Function handle action -----------------------------

exp.createPlayer = async function (payload, cb) {
    // if (!payload.user_id || !payload.nick_name || !payload.uid) {
    if (!payload.id || !payload.nick_name) {
        return cb(false, Code.BAD_REQUEST, "Content can not be empty!");
        // UTILS.invokeCallback(cb, Code.BAD_REQUEST, "Content can not be empty!");
    }
    try {
        const id            = payload?.id ?? 0;
        const nickName      = payload?.nick_name ?? '';
        const uid           = payload?.uid ?? 0;
        const displayName   = payload?.display_name ?? '';
        const avatar        = payload?.avatar ?? '';
        const winNr         = 0; // tổng số ván thắng
        const winRate       = 0;
        const lost          = 0; // tổng số ván thua
        const totalNr       = 0; // tổng số ván đã đánh
        const rank          = 0; // xếp hạng
        const exp           = 0; // điểm kinh nghiệm
        const level         = 0; // level
        const vippoint      = 0; // điểm vip
        const small_speaker = 0; // số lượng loa nhỏ
        const big_speaker   = 0; // số lượng loa lớn
        const type = payload?.type || 'WEB';
        const balance = payload?.balance || 0;

        const newPlayer = {
            id: id,
            nick_name: nickName,
            uid: uid,
            display_name: displayName,
            balance: balance,
            avatar: avatar,
            win: winNr,
            win_rate: winRate,
            lost: lost,
            total: totalNr,
            rank: rank,
            exp: exp,
            level: level,
            vip_point: vippoint,
            type: type,
            small_speaker: small_speaker,
            big_speaker: big_speaker,
            created_at: new Date(),
            updated_at: new Date()
        };
        const resPlayerCreate = await Player.create(newPlayer);
        logger.info("createPlayer >> create player >> data: ", resPlayerCreate);
        if (resPlayerCreate) {
            const player = new PlayerDTO(resPlayerCreate.dataValues);

            // create properties for player
            // ----------------------------------------------------------
            const lastTimeSpin = UTILS.getCurrentTimestamp(); // thời gian quay gần nhất
            const newProperties = {
                id: id,
                day_play_num: 0, // số ván chơi trong ngày
                day_win_num: 0, // số ván thắng trong ngày
                day_lost_num: 0, // số ván thua trong ngày
                spin_num: 1, // số lượt quay
                last_time_spin: lastTimeSpin, // new Date(),
                continuous_login_num: 1, // số lần đăng nhập liên tục
                continuous_spin_num: 0,
                is_first_pay: false, // trạng thái lần đầu nạp tiền (mặc định false chưa nạp)
                small_speaker: small_speaker,
                big_speaker: big_speaker,
                play_json: '{}',
                created_at: new Date(),
                updated_at: new Date(),
                tutorial_completed: 0 // trạng thái hoàn thành giáo trình, mặc định = 0
            };
            const resPropertiesCreate = await Properties.create(newProperties);
            logger.info("createPlayer >> create properties >> data: ", resPropertiesCreate);

            // get properties of user by user_id
            // ----------------------------------------------------------
            const user = await Properties.findOne({ where: { id: id } });
            logger.info("createPlayer >> properties get data: ", user);

            // player.properties = new Properties(user);
            player.properties = new PropertyDTO(user.dataValues)
            logger.info("createPlayer >> player cuối trước khi callback: ", player);
            return cb(null, Code.OK, player);
            // UTILS.invokeCallback(cb, null, player);
        } // end if (resPlayerCreate)

        return cb(true, Code.FAIL, null);
        // UTILS.invokeCallback(cb, null, null);

    } catch (error) {
        logger.info("createPlayer >> error: ", error);
        return cb(false, Code.FAIL, error);
        // UTILS.invokeCallback(cb, error.message, null);
    }
}

exp.getPropertyById = async function(id, cb) {
    try {
        const item = await Properties.findOne({
            where: {
                id: id
            },
            include: [{
                model: Player,
                as: 'player', // bạn phải định nghĩa association với alias là 'player'
                required: false // LEFT JOIN
            }],
            raw: false // Cần false để có thể truy cập các method của instance
        });

        if (!item) {
            cb(null, Code.NOT_FOUND, null);
            return;
        }
        // logger.info("getPropertyById >> data player: ", item.player);
        const player = new PlayerDTO(item?.player);
        // logger.info("getPropertyById >> data player: b: ", b);
        const property = new PropertyDTO(item.dataValues);
        property.player = player;

        cb(null, Code.OK, property);
    } catch (err) {
        cb(null, Code.FAIL, err.message);
    }
};

exp.getPropertiesByIds = async function(ids, cb) {
    // logger.info("getPropertiesByIds >> uids: ", uids);
    try {
        // Kiểm tra input
        if (!Array.isArray(ids) || ids.length === 0) {
            return cb(null, Code.BAD_REQUEST, 'Input phải là một mảng user_id không rỗng');
        }

        const properties = await Properties.findAll({
            where: {
                id: {
                    [Op.in]: ids
                }
            },
            include: [{
                model: Player,
                as: 'player', // bạn phải định nghĩa association với alias là 'player'
                required: false // LEFT JOIN
            }],
            raw: false // Cần false để có thể truy cập các method của instance
        });

        // Chuyển đổi các đối tượng properties thành các đối tượng PropertyDTO
        logger.info("getPropertiesByIds >> properties: ", properties);

        const propertyDTOs = properties.map((property) => {
            const item = new PropertyDTO(property.dataValues);
            const player = new PlayerDTO(property?.player);
            item.player = player;

            return item;
        });

        return cb(null, Code.OK, propertyDTOs);
    } catch (err) {
        logger.error("getPropertiesByIds >> error: ", err);
        throw err;
        // cb(null, Code.FAIL, 'Lỗi trong quá trình truy vấn:' + err.message);
    }
};

exp.changeNickName = function(msg,cb){
    Player.findOne({where:{nick_name: msg.nickName}},function(err, result){
        // if(result === [] || result === {} || !!!result) {
        if (result == null || (Array.isArray(result) && result.length === 0)) {
            Player.update(msg.userID, { nickName: msg.nickName},function(){});
            cb(Code.OK, result);
        }else{
            cb(DECodeF.FAIL, result);
        }
    });
};

exp.getPlayerData = function(id, cb) {
    logger.info("getPlayerData", id);

    Player.findOne({
        where: {
            id: id
        }
    }).then(data => {
        logger.info("getPlayerData >> data: ", data);
        if (data) {
            cb(null, Code.OK, data);
        } else {
            cb(null, Code.NOT_FOUND, null);
        }
    }).catch(err => {
        logger.info("getPlayerData >> err: ", err);

        cb(null, Code.FAIL, err);
    });
};

exp.getPlayerByUid = async function(uid, cb) {
    try {
        const player = await Player.findOne({
            // where: { user_id: userId },
            where: { uid: uid },
            include: [{
                model: Properties,
                as: 'properties', // Giả sử bạn đã định nghĩa association với alias là 'property'
                required: false // LEFT JOIN
            }],
            raw: false // Cần false để có thể truy cập các method của instance
            // include: [{
            //     model: Users,
            //     attributes: ['username', 'id', 'fullname', 'last_login', 'type']
            // }]
        });
        // logger.info("getPlayerByUid >> player222: ", player);
        if (!player) {
            cb(null, Code.NOT_FOUND, null);
            return;
        }

        // const user = new Users(player.dataValues);
        const user = new PlayerDTO(player?.dataValues);
        const properties = new PropertyDTO(player?.properties);

        // user.properties = properties.dataValues;
        user.properties = properties;
        logger.info("getPlayerByUid >> user: ", user);
        // UTILS.invokeCallback(cb, null, user);
        cb(null, Code.OK, user);
        return;
    } catch (err) {
        logger.error("getPlayerByUid >> error: ", err);
        // UTILS.invokeCallback(cb, err.message, null);
        cb(null, Code.FAIL, err.message);
        return;
    }
};

exp.getPlayerById = async function(id, cb) {
    try {
        const player = await Player.findOne({
            where: { id: id },
            include: [{
                model: Properties,
                as: 'properties', // Giả sử bạn đã định nghĩa association với alias là 'property'
                required: false // LEFT JOIN
            }],
            raw: false // Cần false để có thể truy cập các method của instance
            // include: [{
            //     model: Users,
            //     attributes: ['username', 'id', 'fullname', 'last_login', 'type']
            // }]
        });
        // logger.info("getPlayerById >> player222: ", player);
        if (!player) {
            cb(null, Code.NOT_FOUND, null);
            return;
        }

        // const user = new Users(player.dataValues);
        const user = new PlayerDTO(player?.dataValues);
        const properties = new PropertyDTO(player?.properties);

        // user.properties = properties.dataValues;
        user.properties = properties;
        logger.info("getPlayerById >> user: ", user);
        // UTILS.invokeCallback(cb, null, user);
        cb(null, Code.OK, user);
        return;
    } catch (err) {
        logger.error("getPlayerById >> error: ", err);
        // UTILS.invokeCallback(cb, err.message, null);
        cb(null, Code.FAIL, err.message);
        return;
    }
};

exp.getPlayersByIds = async function(ids, cb) {
    logger.info("[dbManager.getPlayersByIds] >> ids: ", ids);
    try {
        // Kiểm tra input
        if (!Array.isArray(ids) || ids.length === 0) {
            cb(null, Code.BAD_REQUEST, 'Input phải là một mảng id không rỗng');
        }

        const players = await Player.findAll({
            where: {
                id: {
                    [Op.in]: ids
                }
            },
            include: [{
                model: Properties,
                as: 'properties', // Giả sử bạn đã định nghĩa association với alias là 'property'
                required: false // LEFT JOIN
            }],
            raw: false // Cần false để có thể truy cập các method của instance
        });
        logger.info("[dbManager.getPlayersByIds] >> players: ", players);
        cb(null, Code.OK, players);
        return;
    } catch (err) {
        logger.error("[dbManager.getPlayersByIds] >> error: ", err);
        cb(null, Code.FAIL, 'Lỗi trong quá trình truy vấn:' + err.message);
        return;
    }
};

exp.updatePlayer = function(id, payload, cb){
    // Player.bulkUpdate({ where:{id:userIDs}, update:{ room_id:roomId} } ,function(rid){
    //     cb(userIDs.length);
    // });
    logger.info("[dbManager.updatePlayer] >> id: ", id);
    logger.info("[dbManager.updatePlayer] >> payload: ", payload);
    try {
        // let id = req.params.id;
        // let body = req.body;
        Player.update(payload, {
            where: {
                id: id
            }
        }).then(data => {
            // return data;
            logger.info("[dbManager.updatePlayer] >> data: ", data);
            if (data[0] === 0) {
                cb(null, Code.NOT_FOUND, "No user found with this id");
                return;
            } else {
                cb(null, Code.OK, data);
                return;
            }
        }).catch(err => {
            logger.info("[dbManager.updatePlayer] >> err: ", err);
            cb(null, Code.FAIL, err);
            return;
        });

    } catch (error) {
        logger.info("[dbManager.updatePlayer] >> catch >> error: ", error);
        cb(null, Code.FAIL, error);
        return;
    }
};

exp.incrementPlayerStats = function(id, payload, cb){
    logger.info("[dbManager.incrementPlayerStats] >> id: ", id);
    logger.info("[dbManager.incrementPlayerStats] >> payload: ", payload);
    try {
        // Prepare the update object with Sequelize.literal for incrementing values
        const updateObj = {};

        if (payload.balance !== undefined) {
            updateObj.balance = Sequelize.literal(`balance + ${payload.balance}`);
        }

        if (payload.total !== undefined) {
            updateObj.total = Sequelize.literal(`total + ${payload.total}`);
        }

        if (payload.win !== undefined) {
            updateObj.win = Sequelize.literal(`win + ${payload.win}`);
        }

        if (payload.lost !== undefined) {
            updateObj.lost = Sequelize.literal(`lost + ${payload.lost}`);
        }

        if (payload.exp !== undefined) {
            updateObj.exp = Sequelize.literal(`exp + ${payload.exp}`);
        }

        // Calculate win_rate if we're updating win or total
        if ((payload.win !== undefined || payload.lost !== undefined) &&
            (payload.total !== undefined || payload.win !== undefined || payload.lost !== undefined)) {
            updateObj.win_rate = Sequelize.literal(`(win / total) * 100`);
        }

        Player.update(updateObj, {
            where: {
                id: id
            }
        }).then(data => {
            logger.info("[dbManager.incrementPlayerStats] >> data: ", data);
            if (data[0] === 0) {
                cb(null, Code.NOT_FOUND, "No user found with this id");
                return;
            } else {
                cb(null, Code.OK, data);
                return;
            }
        }).catch(err => {
            logger.error("[dbManager.incrementPlayerStats] >> err: ", err, ' -> message: ', err.message, ' -> stack: ', err.stack);
            cb(null, Code.FAIL, err);
            return;
        });

    } catch (error) {
        logger.error("[dbManager.incrementPlayerStats] >> catch >> error: ", error, ' -> message: ', error.message, ' -> stack: ', error.stack);
        cb(null, Code.FAIL, error);
        return;
    }
};

exp.updateProperties = function(id, payload, cb) {
    logger.info("[dbManager.updateProperties][0] updateProperties >> id: ", id);
    logger.info("[dbManager.updateProperties][0] updateProperties >> payload: ", payload);
    try {
        Properties.update(payload, {
            where: {
                id: id
            }
        }).then(data => {
            // return data;
            logger.info("[dbManager.updateProperties][1] updateProperties >> data: ", data);
            if (data[0] === 0) {
                cb(null, Code.NOT_FOUND, "No user found with this id");
                return;
            } else {
                cb(null, Code.OK, data);
                return;
            }
        }).catch(err => {
            logger.info("[dbManager.updateProperties][2] updateProperties >> err: ", err);
            cb(null, Code.FAIL, err);
            return;
        });

    } catch (error) {
        logger.info("[dbManager.updateProperties][3] updateProperties catch >> error: ", error);
        cb(null, Code.FAIL, error);
        return;
    }
};

exp.createLogs = function(users, logs, cb) {
    logger.info("[dbManager.createLogs] >> users: ", users);
    logger.info("[dbManager.createLogs] >> logs: ", logs);
    const me = this;
    try {
        const createdAt = new Date(); // UTILS.getCurrentTimestamp();
        const payload = {
            data: UTILS.jsonEndcode(logs).toString(), // logs,
            // created_at: createdAt,
            created_at: UTILS.getCurrentDateTime(7, 'YYYY-MM-DD HH24:MI:SS'),
        };
        logger.info("[dbManager.createLogs] >> payload: ", payload);
        LogsGame.create(payload)
            .then(data => {
                logger.info("[dbManager.createLogs] >> data: ", data);
                // cb(null, Code.OK, data);
                const lid = data?.id ?? 0;
                const gameMode = logs?.gameMode || 'normal'; // sng

                logger.info("[dbManager.createLogs] >> lid: ", lid, ' -> gameMode: ', gameMode);

                let listUserIds = [];
                /**
                 * if create logs_game successfully
                 * then get last id of logs_games insert into lid_uid table with list users input
                 */
                for(let k = 0 ;k < users.length; k++) {
                    me.createLidUid(users[k].id, lid, function (err, code, res) {
                        // logger.info("[x] createLidUidAAAA >> res: ", res, ' err: ', err, ' code: ', code);
                    });
                    listUserIds.push(users[k].id);
                }

                var dataReturn = {
                    code: 200,
                    data: listUserIds,
                    lid: lid // id của logs_games
                };

                // cập nhật lại thông tin tiền + ván đánh cho từng user trong ván đánh này
                // --------------------------------------------------------------------------------------------------------
                let tmpCount = 1;
                // const totalPlayer = logs.gameWinners[0]?.players?.length || 0;
                const totalPlayer = logs.players?.length || 0;
                logger.info("[dbManager.createLogs] >> totalPlayer: ", totalPlayer);
                // for(var i=0; i < logs.gameWinners[0].players.length; i += 1) {
                const playersInGameWinners = logs.gameWinners[0]?.players || [];
                logger.info("[dbManager.createLogs] >> playersInGameWinners: ", playersInGameWinners);
                for(let i = 0; i < totalPlayer; i += 1) {
                    // const _player = logs.gameWinners[0].players[i];
                    let _player = playersInGameWinners.length > 0 ? playersInGameWinners[i] : null;
                    logger.info(i, " -> [dbManager.createLogs] >> _player: ", _player);

                    if (!_player) {
                        let _playerItem = logs.players[i];
                        const sidepots = logs.game?.sidepots || [];
                        const playerWinnerInSidePots = winnerUtils.getMainPotWinnerOfSidepots(sidepots);
                        // So sánh id để xác định xem _playerItem có phải là winner không
                        if (_playerItem && playerWinnerInSidePots && _playerItem.id === playerWinnerInSidePots.id) {
                            _playerItem.isWinner = true;
                        } else {
                            if (_playerItem) {
                                _playerItem.isWinner = false;
                            }
                        }
                        playerWinnerInSidePots.hand.cards = _playerItem?.cards || []
                        _playerItem.amount = playerWinnerInSidePots?.amount || 0;
                        _playerItem.hand = playerWinnerInSidePots?.hand || {};
                        _player = _playerItem;

                        logger.info(i, " -> [dbManager.createLogs] >> _playerItem: ", _playerItem, ' -> _player: ', _player);
                    }
                    
                    // check xem player này có trong danh sách thắng (đc hoàn tiền hay không )
                    // ----------------------------------------------------------------------------------------------------
                    const _checkPlayerInWinner = _.find(logs.gameWinners, function (e) {
                        return e.id == _player.id && e.id != logs.gameWinners[0].id;
                    });
                    logger.info(i, " -> [x] _checkPlayerInWinner: ", _checkPlayerInWinner);
                    logger.info(i, " -> [x] Check player ", _player.playerName, "(", _player.id, ") => _checkPlayerInWinner: ", _checkPlayerInWinner);
                    let _amount = _player?.amount || 0;

                    if (typeof _checkPlayerInWinner !== "undefined") {
                        if (_checkPlayerInWinner.hand.rank != logs.gameWinners[0].hand.rank) {
                            _amount = _checkPlayerInWinner.amount - Math.abs(_player.amount);
                            logger.info("[x] In gia tri _checkPlayerInWinner.amount ", _checkPlayerInWinner.amount, " => Math.abs(_player.amount): ", Math.abs(_player.amount));
                            logger.info("[x] do khác undefined => tính toán lại số tiền >> _amount: ", _amount);
                        }else {
                            _amount = 0;
                        }
                    }

                    // cập nhật lại total và số ván thắng, balance trong table: players, nếu gameModel = sng thì bỏ qua (vì đang dùng tiền hệ thống cấp)
                    // --------------------------------------------------------------------------------------------------------
                    if (gameMode !== 'sng') {
                        const _dataTmp = {
                            balance: _amount, //_player.amount,
                            total: 1, // tổng số ván chơi thêm 1 ván
                            win: (_player.isWinner == true) ? 1 : 0, // số ván thắng thêm 1 ván hoặc 0
                            // exp: _exp,
                        };
                        logger.info(i, " -> [dbManager.createLogs] >> _dataTmp: ", _dataTmp);
                        me.incrementPlayerStats(_player.id, _dataTmp, function (e, ret) {
                            logger.info("[dbManager.createLogs] >> incrementPlayerStats >> ret: ", ret, " e: ", e);
                            if (tmpCount == totalPlayer) {
                                logger.info("tmpCount(", tmpCount,") == totalPlayer(", totalPlayer,") => return ", dataReturn);
                                // utils.invokeCallback(cb, null, dataReturn);
                                cb(null, Code.OK, dataReturn);
                                return;
                            }
                            tmpCount = tmpCount + 1;
                            logger.info("[dbManager.createLogs] >> endGame update lai id ", _player.id, " ret ", ret, " e ", e, " and tmpCount : ", tmpCount, " and totalPlayer: ", totalPlayer);
                        })
                    } // end check gameMode !== sng

                    // call sync add logs transaction => nếu là gameMode = sng thì không cần ghi logs vào transactions table
                    // --------------------------------------------------------------------------------------------------------
                    if (gameMode !== 'sng') {
                        try {
                            const syncService = pomelo.app.get('sync');
                            const dbService = new DbService(pomelo.app);
                            logger.info('[dbManager.createLogs] >> dbService: ', dbService, ' -> pomelo.app: ', pomelo.app);

                            /* dbService.recordTransaction({
                                player_id: _player.id,
                                amount: _amount,
                                type: LogTypes.GAME,
                                action: LogBoards.GAME,
                                reference_id: lid,
                                reference_type: 'logs_game',
                                meta: {
                                    game_id: logs?.id || null,
                                    table_id: logs?.tid || null,
                                    player_name: _player.playerName,
                                    is_winner: _player.isWinner,
                                    hand_info: _player.hand
                                },
                                description: `Cập nhật tiền sau khi kết thúc ván đánh`
                            }) */

                            dbService.recordTransaction({
                                player_id: _player.id,
                                amount: _amount,
                                type: LogTypes.GAME,
                                action: LogBoards.GAME,
                                reference_id: lid,
                                reference_type: 'logs_game',
                                meta: {
                                    game_id: logs?.id || null,
                                    table_id: logs?.tid || null,
                                    player_name: _player.playerName,
                                    is_winner: _player.isWinner,
                                    hand_info: _player.hand
                                },
                                description: `Cập nhật tiền sau khi kết thúc ván đánh`
                            }, (err, code, result) => {
                                if (err || code !== 200) {
                                    logger.error('[dbManager.createLogs] Error recording transaction for player:', _player.id, ' -> err: ', err || result, ' -> message: ', err?.message, ' -> stack: ', err?.stack);
                                    // resolve(null);
                                } else {
                                    logger.info('[dbManager.createLogs] Transaction recorded successfully for player:', _player.id, ' -> result: ', result);
                                    // resolve(result);
                                }
                            });

                            /* if (syncService && typeof syncService.flush === 'function') {
                                syncService.flush('playerSync.recordTransaction', _player.id, {
                                    player_id: _player.id,
                                    amount: _amount,
                                    type: LogTypes.GAME,
                                    action: LogBoards.GAME,
                                    reference_id: lid,
                                    reference_type: 'logs_game',
                                    meta: {
                                        game_id: logs?.id || null,
                                        table_id: logs?.tid || null,
                                        player_name: _player.playerName,
                                        is_winner: _player.isWinner,
                                        hand_info: _player.hand
                                    },
                                    description: `Cập nhật tiền sau khi kết thúc ván đánh`
                                });
                            } else {
                                logger.warn("[createLogs] Sync service not available, skipping transaction log for player id:", _player.id);
                            } */

                        } catch (syncError) {
                            logger.error("[createLogs] Error calling sync service:", syncError.message);
                            // Continue execution even if sync fails
                        }
                    }

                } // end for totalPlayer

            }).catch(err => {
                logger.error("createLogs >> err: ", err, ' -> message: ', err.message, ' -> stack: ', err.stack);
                cb(null, Code.FAIL, err);
                return;
            });

    } catch (error) {
        logger.error("createLogs catch >> error: ", error, ' -> message: ', error.message, ' -> stack: ', error.stack);
        cb(null, Code.FAIL, error);
        return;
    }
};

exp.createLidUid = function(playerId, lid, cb) {
    logger.info("createLidUid >> input parmas: playerId: ", playerId);
    logger.info("createLidUid >> input parmas: lid: ", lid);
    try {
        const payload = {
            player_id: playerId,
            lid: lid,
            created_at: UTILS.getCurrentDateTime(7, 'YYYY-MM-DD HH24:MI:SS'),
            // created_at: new Date().toFormat('YYYY-MM-DD HH24:MI:SS'), // Chuyển đổi định dạng datetime
            type: 0
        }
        logger.info("createLidUid >> payload: ", payload);
        LidUids.create(payload)
            .then(data => {
                logger.info("createLidUid >> data: ", data);
                return cb(null, Code.OK, data);
            }).catch(err => {
                logger.info("createLidUid >> err: ", err.message);
                return cb(null, Code.FAIL, err);
            });

    } catch (error) {
        logger.info("createLidUid catch >> error: ", error);
        return cb(null, Code.FAIL, error);
    }
};

exp.searchPlayer = async function(payload, cb) {
    logger.info("[dbManager.searchPlayer] >> payload: ", payload);
    const playerId    = payload?.excluded_id ?? 0;
    // const type      = payload?.type ?? 2;
    const page      = payload?.page ?? 1;
    const limit     = payload?.limit ?? 10;
    const keyword   = payload?.keyword ?? '';

    // if (!playerId) {
    //     return cb(false, Code.BAD_REQUEST, "Content can not be empty!");
    // }

    const offset    = (page - 1) * limit;

    try {
        const { count, rows } = await Player.findAndCountAll({
            where: {
                id: {
                    [Op.ne]: playerId // Ensure we don't include the searching player
                },
                [Op.or]: [
                    { 'nick_name': { [Op.like]: `%${keyword}%` } },
                    { 'uid': keyword },
                    { 'id': keyword }
                ]
            },
            offset: offset,
            limit: limit,
            raw: false,
            logging: console.log // Thêm dòng này để in ra câu lệnh SQL query
        });

        // Chuyển đổi rows thành FriendDTO
        const friends = rows.map(item => {
            try {
                return new PlayerLiteDTO(item.dataValues);
            } catch (err) {
                logger.error('Error converting to FriendDTO:', err);
                return item.dataValues;
            }
        });

        const totalPages = Math.ceil(count / limit);
        const result = {
            totalItems: count,
            totalPages: totalPages,
            currentPage: page,
            friends: friends
        };

        return cb(null, Code.OK, result);
    } catch (error) {
        logger.error("searchPlayer >> error: ", UTILS.jsonEndcode(error));
        return cb(false, Code.FAIL, error);
    }
}

// ----------------------------------- Friends --------------------------------------------------------------

/**
 * Kiểm tra xem user_id và f_user_id đã là bạn bè chưa
 * @param {number} user_id - ID của người dùng
 * @param {number} f_user_id - ID của người bạn
 * @returns {Promise<boolean>} - Trả về true nếu đã là bạn bè, ngược lại false
 */
// exp.checkFriendExist = async function(user_id, f_user_id, cb) {
exp.checkFriendExist = async function(payload, cb) {
    logger.info("[dbManager.checkFriendExist] >> payload: ", payload);
    try {
        // const friend = await Friends.findOne({
        //     where: {
        //         user_id: user_id,
        //         f_user_id: f_user_id,
        //         type: 2 // type = 2 nghĩa là đã là bạn bè
        //     }
        // });
        const friend = await Friends.findOne({
            where: payload
        });

        // return !!friend; // Trả về true nếu tìm thấy bản ghi, ngược lại false
        return cb(null, Code.OK, !!friend);
    } catch (error) {
        logger.error('Error checking friend existence:', error);
        // throw error;
        return cb(null, Code.FAIL, error);
    }
}

exp.deleteFriend = async function(payload, cb) {
    try {
        let data = await Friends.destroy({
            where: {
                player_id: payload.uid,
                f_player_id: payload.fid
            },
            // truncate: true
        });

        return cb(null, Code.OK, data);
    } catch (error) {
        logger.error('deleteFriend error:', error);
        // throw error;
        return cb(null, Code.FAIL, error);
    }
}

/**
 * Hàm thực hiện xóa 1 item theo điều kiện truyền vào qua tham số 
 * @param {Object} payload 
 * @param {*} cb 
 * @returns 
 */
exp.destroyItemFriendWithWhereParams = async function(payload, cb) {
    try {
        let data = await Friends.destroy({
            where: payload
        });

        return cb(null, Code.OK, data);
    } catch (error) {
        logger.error('destroyItemFriendWithWhereParams error:', error, ' -> message: ', error.message, ' -> stack: ', error.stack);
        // throw error;
        return cb(null, Code.FAIL, error);
    }
}

exp.getFriendByUserIdAndFriendUserId = async function(playerId, friendPlayerId, cb) {
    try {
        const item = await Friends.findOne({
            where: {
                player_id: playerId,
                f_player_id: friendPlayerId
            },
            // include: [{
            //     model: Player,
            //     as: 'player', // bạn phải định nghĩa association với alias là 'player'
            //     required: false // LEFT JOIN
            // }],
            // raw: false // Cần false để có thể truy cập các method của instance
        });

        if (!item) {
            cb(null, Code.NOT_FOUND, null);
            return;
        }
        // const player = new PlayerDTO(item?.player);
        // logger.info("getPropertyByUserId >> data player: b: ", b);
        // const property = new PropertyDTO(item.dataValues);
        // property.player = player;

        cb(null, Code.OK, item);
        return;
    } catch (err) {
        cb(null, Code.FAIL, err.message);
        return;
    }
};

exp.addFriend = async function (payload, cb) {
    if (!payload.uid || !payload.fid) {
        cb(false, Code.BAD_REQUEST, "Content can not be empty!");
        return;
    }
    try {
        const playerId      = payload?.uid ?? null;
        const fPlayerId     = payload?.fid ?? null;
        const type          = payload?.type ?? 1;
        const createdAt     = payload?.created_at ?? UTILS.getCurrentDateTime(0, 'YYYY-MM-DD HH24:MI:SS');
        const updatedAt     = payload?.updated_at ?? UTILS.getCurrentDateTime(0, 'YYYY-MM-DD HH24:MI:SS');

        const newItem = {
            player_id: playerId,
            f_player_id: fPlayerId,
            type: type, // 0: rejected,  1: waiting, 2: accepted, 3: blocked
            created_at: createdAt,
            updated_at: updatedAt
        };

        const resCreate = await Friends.create(newItem);
        logger.info("addFriend >> create success: ", !!resCreate);

        if (!resCreate) {
            return cb(false, Code.FAIL, "Failed to create friend record");
        }

        // Trả về dữ liệu đã được tạo ở định dạng đơn giản
        return cb(null, Code.OK, {
            id: resCreate.id,
            player_id: resCreate.player_id,
            f_player_id: resCreate.f_player_id,
            type: resCreate.type,
            created_at: resCreate.created_at,
            updated_at: resCreate.updated_at
        });
    } catch (error) {
        logger.error("addFriend >> error: ", UTILS.jsonEndcode(error));
        return cb(false, Code.FAIL, error);
    }
}

exp.updateFriendType = async function (payload, cb) {
    logger.info("[dbManager.updateFriendType] >> payload: ", payload);
    if (!payload.uid || !payload.fid) {
        cb(false, Code.BAD_REQUEST, "Content can not be empty!");
    }
    try {
        const playerId      = payload?.uid ?? null;
        const fPlayerId     = payload?.fid ?? null;
        const type          = payload?.type ?? 1;

        const itemUpdate = {
            type: type, // 0: rejected,  1: waiting, 2: accepted, 3: blocked
            updated_at: UTILS.getCurrentDateTime(7, 'YYYY-MM-DD HH24:MI:SS')
        };

        Friends.update(itemUpdate, {
            where: {
                player_id: playerId,
                f_player_id: fPlayerId
            }
        }).then(async data => {
            // return data;
            logger.info("updateFriendType >> data: ", data);
            if (data[0] === 0) {
                return cb(null, Code.NOT_FOUND, "No found with this id");
            } else {
                const item = await Friends.findOne({
                    where: {
                        player_id: playerId,
                        f_player_id: fPlayerId
                    },
                    raw: true // Lấy dữ liệu raw để tránh vấn đề với Sequelize model
                });

                logger.info("updateFriendType >> item found: ", !!item);

                if (!item) {
                    return cb(null, Code.NOT_FOUND, "Friend record not found after update");
                }

                try {
                    // Sử dụng FriendDTO cải tiến
                    const friendDTO = new FriendDTO(item);
                    return cb(null, Code.OK, friendDTO.toJSON());
                } catch (error) {
                    logger.error("updateFriendType >> Error creating DTO: ", error.message);
                    // Trả về dữ liệu raw nếu không thể tạo DTO
                    return cb(null, Code.OK, {
                        id: item.id,
                        player_id: item.player_id,
                        f_player_id: item.f_player_id,
                        type: item.type,
                        created_at: item.created_at,
                        updated_at: item.updated_at
                    });
                }
            }
        }).catch(err => {
            logger.info("updateFriendType >> err: ", err);
            return cb(null, Code.FAIL, err);
        });
    } catch (error) {
        logger.error("updateFriendType >> error: ", UTILS.jsonEndcode(error));
        return cb(false, Code.FAIL, error);
    }
}

exp.getFriends = async function(payload, cb) {
    const playerId    = payload?.uid ?? null;
    const page      = payload?.page ?? 1;
    const limit     = payload?.limit ?? 10;

    if (!playerId) {
        return cb(false, Code.BAD_REQUEST, "Content can not be empty!");
    }

    const offset    = (page - 1) * limit;

    try {
        const { count, rows } = await Friends.findAndCountAll({
            where: {
                player_id: playerId,
                type: 2
            },
            offset: offset,
            limit: limit,
            include: [{
                model: Player,
                as: 'player', // bạn phải định nghĩa association với alias là 'player'
                required: false // LEFT JOIN
            }],
            raw: false // Cần false để có thể truy cập các method của instance
        });

        // Chuyển đổi rows thành FriendDTO
        const friends = rows.map(item => {
            try {
                return new FriendDTO(item.dataValues);
            } catch (err) {
                logger.error('Error converting to FriendDTO:', err);
                return item.dataValues;
            }
        });

        const totalPages = Math.ceil(count / limit);
        const result = {
            totalItems: count,
            totalPages: totalPages,
            currentPage: page,
            friends: friends
        };

        return cb(null, Code.OK, result);
    } catch (error) {
        logger.error("getFriends >> error: ", UTILS.jsonEndcode(error));
        return cb(false, Code.FAIL, error);
    }
}

exp.searchFriends = async function(payload, cb) {
    logger.info("[dbManager.searchFriends] >> payload: ", payload);
    const playerId    = payload?.uid ?? null;
    const type      = payload?.type ?? 2;
    const page      = payload?.page ?? 1;
    const limit     = payload?.limit ?? 10;
    const keyword   = payload?.keyword ?? '';

    if (!playerId) {
        return cb(false, Code.BAD_REQUEST, "Content can not be empty!");
    }

    const offset    = (page - 1) * limit;

    try {

        // Xây dựng điều kiện truy vấn
        const whereCondition = { 
            player_id: playerId,
            f_player_id: playerId,
            type: type,
            [Op.or]: [
                { '$player.nick_name$': { [Op.like]: `%${keyword}%` } },
                { '$player.uid$': keyword },
                { '$player.id$': keyword },
                { '$player_invite.nick_name$': { [Op.like]: `%${keyword}%` } },
                { '$player_invite.uid$': keyword },
                { '$player_invite.id$': keyword }
            ]
        };
        
        if (type === 0) {
            delete whereCondition.type;
            delete whereCondition.f_player_id;
        }

        if (type === 1) {
            delete whereCondition.player_id;
            whereCondition.f_player_id = playerId;
        }

        if (type === 2) {
            delete whereCondition.f_player_id;
        }

        if (type === 3) {
            whereCondition.type = 1;
            delete whereCondition.f_player_id;
        }

        if (type === 4) {
            whereCondition.type = 3;
            delete whereCondition.f_player_id;
        }
        logger.info("searchFriends >> whereCondition: ", JSON.stringify(whereCondition));
        const { count, rows } = await Friends.findAndCountAll({
            // where: {
            //     player_id: playerId,
            //     type: type,
            //     [Op.or]: [
            //         { '$player.nick_name$': { [Op.like]: `%${keyword}%` } },
            //         { '$player.uid$': keyword },
            //         { '$player.id$': keyword }
            //     ]
            // },
            where: whereCondition,
            offset: offset,
            limit: limit,
            include: [{
                model: Player,
                as: 'player',
                required: false
            }, {
                model: Player,
                as: 'player_invite',
                required: false
            }],
            raw: false,
            logging: console.log // Thêm dòng này để in ra câu lệnh SQL query
        });

        // Chuyển đổi rows thành FriendDTO
        const friends = rows.map(item => {
            try {
                return new FriendDTO(item.dataValues);
            } catch (err) {
                logger.error('Error converting to FriendDTO:', err);
                return item.dataValues;
            }
        });

        const totalPages = Math.ceil(count / limit);
        const result = {
            totalItems: count,
            totalPages: totalPages,
            currentPage: page,
            friends: friends
        };

        return cb(null, Code.OK, result);
    } catch (error) {
        logger.error("searchFriends >> error: ", UTILS.jsonEndcode(error));
        return cb(false, Code.FAIL, error);
    }
}

exp.getAllFriends = async function(payload, cb) {
    const userId = payload?.uid ?? null;
    if (!userId) {
        return cb(false, Code.BAD_REQUEST, "Content can not be empty!");
    }

    try {
        const rows = await Friends.findAll({
            where: {
                player_id: userId,
                type: 2
            },
            include: [{
                model: Player,
                as: 'player', // bạn phải định nghĩa association với alias là 'player'
                required: false // LEFT JOIN
            }],
            raw: false // Cần false để có thể truy cập các method của instance
        });

        // Chuyển đổi rows thành FriendDTO
        const friends = rows.map(item => {
            try {
                return new FriendDTO(item.dataValues);
            } catch (err) {
                logger.error('Error converting to FriendDTO:', err);
                return item.dataValues;
            }
        });
        logger.info("getAllFriends >> friends: ", friends);
        return cb(null, Code.OK, friends);
    } catch (error) {
        logger.error("getAllFriends >> error: ", UTILS.jsonEndcode(error));
        return cb(false, Code.FAIL, error);
    }
}

/**
 * 
 * @param {Object} payload 
 * @param {number} payload.uid - User ID
 * @param {number} payload.page - Page number
 * @param {number} payload.limit - Number of items per page
 * @param {Array} payload.types - Type of friends
 * @param {*} cb 
 * @returns 
 */
exp.getFriendsByTypes = async function(payload, cb) {
    logger.info("[dbManager.getFriendsByTypes] >> payload: ", payload);

    const userId = payload?.uid ?? null;
    const page = payload?.page ?? 1; 
    const limit = payload?.limit ?? 10;
    const types = payload?.types ?? [];

    if (!userId) {
        return cb(false, Code.BAD_REQUEST, "Content can not be empty!");
    }

    if (!Array.isArray(types) && typeof types !== 'number') {
        return cb(false, Code.BAD_REQUEST, "Types must be array or number");
    }

    const offset = (page - 1) * limit;
    const typeCondition = Array.isArray(types) ? { [Op.in]: types } : types;

    try {
        const { count, rows } = await Friends.findAndCountAll({
            where: {
                player_id: userId,
                type: typeCondition
            },
            offset: offset,
            limit: limit,
            include: [{
                model: Player,
                as: 'player',
                required: false
            }],
            raw: false
        });

        const friends = rows.map(item => {
            try {
                return new FriendDTO(item.dataValues);
            } catch (err) {
                logger.error('Error converting to FriendDTO:', err);
                return item.dataValues;
            }
        });

        const totalPages = Math.ceil(count / limit);
        const result = {
            totalItems: count,
            totalPages: totalPages, 
            currentPage: page,
            friends: friends
        };
        // logger.info("[dbManager.getFriendsByTypes] >> result: ", result);
        return cb(null, Code.OK, result);
    } catch (error) {
        logger.error("getFriendsByTypes >> error: ", UTILS.jsonEndcode(error));
        return cb(false, Code.FAIL, error);
    }
}


exp.getInviteFriends = async function(payload, cb) {
    logger.info("[dbManager.getInviteFriends] >> payload: ", payload);

    const userId    = payload?.uid ?? null;
    const page      = payload?.page ?? 1;
    const limit     = payload?.limit ?? 10;

    if (!userId) {
        return cb(false, Code.BAD_REQUEST, "Content can not be empty!");
    }

    const offset    = (page - 1) * limit;

    try {
        const { count, rows } = await Friends.findAndCountAll({
            where: {
                f_player_id: userId,
                type: 1
            },
            offset: offset,
            limit: limit,
            include: [{
                model: Player,
                as: 'player', // bạn phải định nghĩa association với alias là 'player'
                required: false // LEFT JOIN
            }, {
                model: Player,
                as: 'player_invite', // bạn phải định nghĩa association với alias là 'player_invite'
                required: false // LEFT JOIN
            }],
            raw: false // Cần false để có thể truy cập các method của instance
        });

        // Chuyển đổi rows thành FriendDTO
        const friends = rows.map(item => {
            try {
                return new FriendDTO(item.dataValues);
            } catch (err) {
                logger.error('Error converting to FriendDTO:', err);
                return item.dataValues;
            }
        });

        const totalPages = Math.ceil(count / limit);
        const result = {
            totalItems: count,
            totalPages: totalPages,
            currentPage: page,
            friends: friends
        };

        return cb(null, Code.OK, result);
    } catch (error) {
        logger.error("getInviteFriends >> error: ", UTILS.jsonEndcode(error));
        return cb(false, Code.FAIL, error);
    }
}

exp.getFollowFriends = async function(payload, cb) {
    logger.info("[dbManager.getFollowFriends] >> payload: ", payload);

    const userId    = payload?.uid ?? null;
    const page      = payload?.page ?? 1;
    const limit     = payload?.limit ?? 10;

    if (!userId) {
        return cb(false, Code.BAD_REQUEST, "Content can not be empty!");
    }

    const offset    = (page - 1) * limit;

    try {
        const { count, rows } = await Friends.findAndCountAll({
            where: {
                player_id: userId,
                type: 1
            },
            offset: offset,
            limit: limit,
            include: [{
                model: Player,
                as: 'player', // bạn phải định nghĩa association với alias là 'player'
                required: false // LEFT JOIN
            }, {
                model: Player,
                as: 'player_invite', // bạn phải định nghĩa association với alias là 'player_invite'
                required: false // LEFT JOIN
            }],
            raw: false // Cần false để có thể truy cập các method của instance
        });

        // Chuyển đổi rows thành FriendDTO
        const friends = rows.map(item => {
            try {
                return new FriendDTO(item.dataValues);
            } catch (err) {
                logger.error('Error converting to FriendDTO:', err);
                return item.dataValues;
            }
        });

        const totalPages = Math.ceil(count / limit);
        const result = {
            totalItems: count,
            totalPages: totalPages,
            currentPage: page,
            friends: friends
        };

        return cb(null, Code.OK, result);
    } catch (error) {
        logger.error("getInviteFriends >> error: ", UTILS.jsonEndcode(error));
        return cb(false, Code.FAIL, error);
    }
}

exp.getBlockFriends = async function(payload, cb) {
    const playerId    = payload?.uid ?? null;
    const page      = payload?.page ?? 1;
    const limit     = payload?.limit ?? 10;

    if (!playerId) {
        return cb(false, Code.BAD_REQUEST, "Content can not be empty!");
    }

    const offset    = (page - 1) * limit;

    try {
        const { count, rows } = await Friends.findAndCountAll({
            where: {
                player_id: playerId,
                type: 3
            },
            offset: offset,
            limit: limit,
            include: [{
                model: Player,
                as: 'player', // bạn phải định nghĩa association với alias là 'player'
                required: false // LEFT JOIN
            }],
            raw: false // Cần false để có thể truy cập các method của instance
        });

        // Chuyển đổi rows thành FriendDTO
        const friends = rows.map(item => {
            try {
                return new FriendDTO(item.dataValues);
            } catch (err) {
                logger.error('Error converting to FriendDTO:', err);
                return item.dataValues;
            }
        });

        const totalPages = Math.ceil(count / limit);
        const result = {
            totalItems: count,
            totalPages: totalPages,
            currentPage: page,
            friends: friends
        };

        return cb(null, Code.OK, result);
    } catch (error) {
        logger.error("getBlockFriends >> error: ", UTILS.jsonEndcode(error));
        return cb(false, Code.FAIL, error);
    }
}

exp.getRandomPlayersNotFriends = async function(payload, cb) {
    logger.info("[dbManager.getRandomPlayersNotFriends] >> payload: ", payload);
    const userId    = payload?.uid ?? null;
    const limit     = payload?.limit ?? 10;
    if (!userId) {
        return cb(false, Code.BAD_REQUEST, "Content can not be empty!");
    }

    try {
        const randomPlayers = await Player.findAll({
            where: {
                // Don't include the user themself
                id: {
                    [Op.ne]: userId,
                    // Use a single notIn with a UNION to exclude all unwanted players
                    [Op.notIn]: Sequelize.literal(`(
                        /* Friends (accepted) in both directions */
                        SELECT f_player_id FROM friends
                        WHERE player_id = ${userId} AND type = 2
                        UNION
                        SELECT player_id FROM friends
                        WHERE f_player_id = ${userId} AND type = 2
                        UNION
                        /* Blocked users */
                        SELECT f_player_id FROM friends
                        WHERE player_id = ${userId} AND type = 3
                        UNION
                        /* Incoming friend requests */
                        SELECT player_id FROM friends
                        WHERE f_player_id = ${userId} AND type = 1
                        UNION
                        /* Outgoing friend requests */
                        SELECT f_player_id FROM friends
                        WHERE player_id = ${userId} AND type = 1
                    )`)
                }
            },
            order: Sequelize.literal('RAND()'),
            limit: limit // Số lượng người chơi ngẫu nhiên muốn lấy
        });
        logger.info("[dbManager.getRandomPlayersNotFriends] >> randomPlayers: ", randomPlayers);
        return cb(null, Code.OK, randomPlayers);

    } catch (error) {
        logger.error("getRandomPlayerNotFriends >> error: ", UTILS.jsonEndcode(error));
        return cb(false, Code.FAIL, error);
    }
}


// ----------------------------------- Mini Lucky Cards --------------------------------------------------------------
// Lấy thông tin bài may mắn ngày hôm nay, với thời gian activeDate truyền vào, nếu không truyền vào thì lấy ngày hiện tại
exp.getMiniLuckyCard = async function(payload, cb) {
    const activeDate    = payload?.activeDate ?? UTILS.getCurrentDateTime(7, 'YYYY-MM-DD');
    logger.info("[dbManager.getMiniLuckyCard] >> activeDate: ", activeDate);
    if (!activeDate) {
        return cb(false, Code.BAD_REQUEST, "Content can not be empty!");
    }

    try {
        const miniLuckyCards = await MiniLuckyCards.findOne({
            where: {
                is_active: 1,
                active_date: {
                    [Op.eq]: activeDate
                }
            }
        });
        logger.info("[dbManager.getMiniLuckyCard] >> miniLuckyCards: ", miniLuckyCards);

        if (!miniLuckyCards) {
            return cb(null, Code.NOT_FOUND, null);
        }

        // Chuyển đổi dữ liệu sang DTO
        const luckyCardDTO = new MiniLuckyCardDTO(miniLuckyCards.dataValues);
        logger.info("[dbManager.getMiniLuckyCard] >> luckyCardDTO: ", luckyCardDTO);

        return cb(null, Code.OK, luckyCardDTO);
    } catch (error) {
        // logger.error("[dbManager.getMiniLuckyCard] >> error: ", UTILS.jsonEndcode(error));
        logger.error("[dbManager.getMiniLuckyCard] >> error: ", error);
        return cb(false, Code.FAIL, error);
    }
}

// Lấy thông tin bài may mắn của ngày hiện tại
exp.getCurrentLuckyCard = async function(cb) {
    const today = UTILS.getCurrentDateTime(7, 'YYYY-MM-DD');
    logger.info("[dbManager.getCurrentLuckyCard] >> today: ", today);

    try {
        const luckyCard = await MiniLuckyCards.findOne({
            where: {
                is_active: 1,
                active_date: {
                    [Op.eq]: today
                }
            }
        });

        if (!luckyCard) {
            return cb(null, Code.NOT_FOUND, null);
        }

        // Chuyển đổi dữ liệu sang DTO
        const luckyCardDTO = new MiniLuckyCardDTO(luckyCard.dataValues);
        logger.info("[dbManager.getCurrentLuckyCard] >> luckyCardDTO: ", luckyCardDTO);

        return cb(null, Code.OK, luckyCardDTO);
    } catch (error) {
        logger.error("[dbManager.getCurrentLuckyCard] >> error: ", UTILS.jsonEndcode(error));
        return cb(false, Code.FAIL, error);
    }
}

// Tạo hoặc cập nhật bài may mắn cho ngày hiện tại
exp.createOrUpdateLuckyCard = async function(payload, cb) {
    if (!payload.card_value || !payload.card_suit || !payload.multiplier) {
        return cb(false, Code.BAD_REQUEST, "Missing required fields");
    }

    const today = UTILS.getCurrentDateTime(7, 'YYYY-MM-DD');
    logger.info("[dbManager.createOrUpdateLuckyCard] >> today: ", today);

    try {
        // Tìm xem đã có bài may mắn cho ngày hôm nay chưa
        let luckyCard = await MiniLuckyCards.findOne({
            where: {
                active_date: {
                    [Op.eq]: today
                }
            }
        });

        // // Nếu đã có, cập nhật lại
        // if (luckyCard) {
        //     const updateData = {
        //         card_value: payload.card_value,
        //         card_suit: payload.card_suit,
        //         multiplier: payload.multiplier,
        //         code: payload.code,
        //         is_active: 1,
        //         updated_at: new Date()
        //     };

        //     await MiniLuckyCards.update(updateData, {
        //         where: { id: luckyCard.id }
        //     });

        //     // Lấy lại dữ liệu sau khi cập nhật
        //     luckyCard = await MiniLuckyCards.findByPk(luckyCard.id);
        // }
        // // Nếu chưa có, tạo mới
        // else {
        //     const newLuckyCard = {
        //         card_value: payload.card_value,
        //         card_suit: payload.card_suit,
        //         multiplier: payload.multiplier,
        //         code: payload.code,
        //         active_date: today,
        //         is_active: 1,
        //         created_at: new Date(),
        //         updated_at: new Date()
        //     };

        //     luckyCard = await MiniLuckyCards.create(newLuckyCard);
        // }

        // Nếu chưa có, tạo mới
        if (!luckyCard) {
            const newLuckyCard = {
                card_value: payload.card_value,
                card_suit: payload.card_suit,
                multiplier: payload.multiplier,
                code: payload.code,
                active_date: today,
                is_active: 1,
                created_at: new Date(),
                updated_at: new Date()
            };

            luckyCard = await MiniLuckyCards.create(newLuckyCard);
        }


        // Chuyển đổi dữ liệu sang DTO
        const luckyCardDTO = new MiniLuckyCardDTO(luckyCard.dataValues);

        return cb(null, Code.OK, luckyCardDTO);
    } catch (error) {
        logger.error("[dbManager.createOrUpdateLuckyCard] >> error: ", UTILS.jsonEndcode(error));
        return cb(false, Code.FAIL, error);
    }
}


// ----------------------------------- Mini Spin History ----------------------------------------------------------
// Lưu lịch sử quay mini game
exp.createSpinHistory = async function(payload, cb) {
    // Kiểm tra payload hợp lệ
    if (!payload.uid || !payload.bet_amount || !payload.cards) {
        logger.error("[dbManager.createSpinHistory] >> Missing required fields in payload:", payload);
        return cb(new Error("Missing required fields"), Code.BAD_REQUEST, null);
    }

    try {
        const spinData = {
            uid: payload.uid,
            bet_level_id: payload.bet_level_id || 1,
            bet_amount: payload.bet_amount,
            hand_type_id: payload.hand_type_id || 0,
            hand_type: payload.hand_type || '',
            cards: payload.cards,
            is_lucky_card: payload.is_lucky_card || false,
            multiplier: payload.multiplier || 1,
            base_reward: payload.base_reward || 0,
            total_reward: payload.total_reward || 0,
            session_status: 'COMPLETED',
            created_at: new Date(),
            updated_at: new Date()
        };

        logger.info("[dbManager.createSpinHistory] >> spinData before create:", spinData);

        const spinHistory = await MiniSpinHistory.create(spinData);
        logger.info("[dbManager.createSpinHistory] >> spinHistory created:", spinHistory ? true : false);

        if (!spinHistory) {
            return cb(new Error("Failed to create spin history"), Code.FAIL, null);
        }

        // Chuyển đổi dữ liệu sang DTO
        const spinHistoryDTO = new MiniSpinHistoryDTO(spinHistory.dataValues);

        return cb(null, Code.OK, spinHistoryDTO);
    } catch (error) {
        logger.error("[dbManager.createSpinHistory] >> error:", error);
        return cb(error, Code.FAIL, null);
    }
}

// Lấy lịch sử quay mini game của một người chơi
exp.getSpinHistory = async function(payload, cb) {
    const uid = payload?.uid;
    const page = payload?.page || 1;
    const pageSize = payload?.pageSize || 10;

    if (!uid) {
        return cb(false, Code.BAD_REQUEST, "Missing user ID");
    }

    const offset = (page - 1) * pageSize;

    try {
        const { count, rows } = await MiniSpinHistory.findAndCountAll({
            where: {
                uid: uid,
                session_status: 'COMPLETED'
            },
            order: [['created_at', 'DESC']],
            offset: offset,
            limit: pageSize
        });

        if (!rows || rows.length === 0) {
            return cb(null, Code.NOT_FOUND, {
                history: [],
                pagination: {
                    currentPage: page,
                    totalPages: 0,
                    totalItems: 0
                }
            });
        }

        // Chuyển đổi tất cả các bản ghi sang DTO
        const spinHistoryDTOs = rows.map(row => new MiniSpinHistoryDTO(row.dataValues));

        // Lấy chỉ những thông tin cần thiết cho API response
        const historyItems = spinHistoryDTOs.map(dto => dto.toJSON());

        const totalPages = Math.ceil(count / pageSize);

        const result = {
            history: historyItems,
            pagination: {
                currentPage: parseInt(page),
                totalPages: totalPages,
                totalItems: count
            }
        };

        return cb(null, Code.OK, result);
    } catch (error) {
        logger.error("[dbManager.getSpinHistory] >> error: ", error);
        return cb(false, Code.FAIL, error);
    }
}

// Lấy thống kê quay mini game của một người chơi
exp.getSpinStats = async function(payload, cb) {
    const uid = payload?.uid;

    if (!uid) {
        return cb(false, Code.BAD_REQUEST, "Missing user ID");
    }

    try {
        // Tổng số lần quay
        const totalSpins = await MiniSpinHistory.count({
            where: {
                uid: uid,
                session_status: 'COMPLETED'
            }
        });

        // Tổng số lần thắng (có phần thưởng > 0)
        const totalWins = await MiniSpinHistory.count({
            where: {
                uid: uid,
                session_status: 'COMPLETED',
                total_reward: { [Op.gt]: 0 }
            }
        });

        // Tổng số tiền đã đặt cược
        const totalBetResult = await MiniSpinHistory.sum('bet_amount', {
            where: {
                uid: uid,
                session_status: 'COMPLETED'
            }
        });
        const totalBet = totalBetResult || 0;

        // Tổng số tiền đã thắng
        const totalWonResult = await MiniSpinHistory.sum('total_reward', {
            where: {
                uid: uid,
                session_status: 'COMPLETED'
            }
        });
        const totalWon = totalWonResult || 0;

        // Bộ bài lớn nhất đã có
        const bestHand = await MiniSpinHistory.findOne({
            where: {
                uid: uid,
                session_status: 'COMPLETED'
            },
            order: [['hand_type_id', 'ASC'], ['total_reward', 'DESC']],
            limit: 1
        });

        // Phần thưởng lớn nhất
        const biggestWin = await MiniSpinHistory.findOne({
            where: {
                uid: uid,
                session_status: 'COMPLETED'
            },
            order: [['total_reward', 'DESC']],
            limit: 1
        });

        const bestHandDTO = bestHand ? new MiniSpinHistoryDTO(bestHand.dataValues) : null;
        const biggestWinDTO = biggestWin ? new MiniSpinHistoryDTO(biggestWin.dataValues) : null;

        const result = {
            totalSpins: totalSpins,
            totalWins: totalWins,
            winRate: totalSpins > 0 ? (totalWins / totalSpins * 100).toFixed(2) + '%' : '0%',
            totalBet: totalBet,
            totalWon: totalWon,
            netProfit: totalWon - totalBet,
            bestHand: bestHandDTO ? {
                handType: bestHandDTO.handType,
                cards: bestHandDTO.cards.map(card => card.code),
                reward: bestHandDTO.totalReward
            } : null,
            biggestWin: biggestWinDTO ? {
                timestamp: biggestWinDTO.timestamp,
                handType: biggestWinDTO.handType,
                cards: biggestWinDTO.cards.map(card => card.code),
                reward: biggestWinDTO.totalReward
            } : null
        };

        return cb(null, Code.OK, result);
    } catch (error) {
        logger.error("[dbManager.getSpinStats] >> error: ", error);
        return cb(false, Code.FAIL, error);
    }
}

//---------------------------- Chat Sessions ------------------------------------------------------------

//1 . viết hàm kiểm tra xem đã có chat_sesions với user1_id và user2_id chưa
exp.checkChatSession = async function(payload, cb) {
    logger.info("[dbManager.checkChatSession] >> payload: ", payload);
    const { user1_id, user2_id } = payload;

    const session = await ChatSessions.findOne({
        where: {
            [Op.or]: [
                { user1_id: user1_id, user2_id: user2_id },
                { user1_id: user2_id, user2_id: user1_id }
            ]
        }
    });
    logger.info("[dbManager.checkChatSession] >> session: ", session);
    return cb(null, Code.OK, session);
}

//2 . tạo chat_session mới
exp.createChatSession = async function(payload, cb) {
    logger.info("[dbManager.createChatSession] >> payload: ", payload);
    const { user1_id, user2_id } = payload;

    try {
        const session = await ChatSessions.create({
            user1_id: user1_id,
            user2_id: user2_id
        });
        logger.info("[dbManager.createChatSession] >> session: ", session);
        return cb(null, Code.OK, session);
    } catch (error) {
        logger.error("[dbManager.createChatSession] >> error: ", error);
        return cb(false, Code.FAIL, error);
    }
}

//3 . tạo hàm gửi tin nhắn bao gồm cả text và file
exp.createMessage = async function(payload, cb) {
    logger.info("[dbManager.createMessage] >> payload: ", payload);
    const { session_id, sender_id, receiver_id, content, file_url, file_type } = payload;

    try {
        const message = await ChatMessages.create({
            session_id: session_id,
            sender_id: sender_id,
            receiver_id: receiver_id,
            message: content,
            file_url: file_url,
            file_type: file_type,
            is_system: false,
            sent_at: new Date()
        });
        logger.info("[dbManager.createMessage] >> message: ", message);
        return cb(null, Code.OK, message);
    } catch (error) {
        logger.error("[dbManager.createMessage] >> error: ", error);
        return cb(false, Code.FAIL, error);
    }
}

//4 . tạo hàm gửi tin nhắn với trường hợp là BOT
exp.createBotMessage = async function(payload, cb) {
    logger.info("[dbManager.createBotMessage] >> payload: ", payload);
    const { session_id, sender_id, content } = payload;

    try {
        const message = await ChatMessages.create({
            session_id: session_id,
            sender_id: sender_id,
            message: content,
            is_system: true,
            sent_at: new Date()
        });
        logger.info("[dbManager.createBotMessage] >> message: ", message);
        return cb(null, Code.OK, message);
    } catch (error) {
        logger.error("[dbManager.createBotMessage] >> error: ", error);
        return cb(false, Code.FAIL, error);
    }
}

//5 . viết hàm lấy lịch sử tin nhắn giữa 2 người có phân trang và check deleted_at is null
exp.getChatHistory = async function(payload, cb) {
    logger.info("[dbManager.getChatHistory] >> payload: ", payload);
    const { session_id, page, pageSize } = payload;

    try {
        const { count, rows } = await ChatMessages.findAndCountAll({
            where: { session_id: session_id, deleted_at: null },
            order: [['sent_at', 'DESC']],
            offset: (page - 1) * pageSize,
            limit: pageSize,
            include: [
                {
                    model: Player,
                    as: 'sender',
                    attributes: ['id', 'nick_name', 'avatar']
                },
                {
                    model: Player,
                    as: 'receiver',
                    attributes: ['id', 'nick_name', 'avatar']
                }
            ]
        });
        return cb(null, Code.OK, {
            count: count,
            rows: rows
        });
    } catch (error) {
        logger.error("[dbManager.getChatHistory] >> error: ", error);
        return cb(false, Code.FAIL, error);
    }
}

//6 . viết hàm lấy các tin nhắn mới chưa đọc của 1 user, nhớ check deleted_at is null, sắp xếp theo sent_at ASC
exp.getNewMessages = async function(payload, cb) {
    logger.info("[dbManager.getNewMessages] >> payload: ", payload);
    const { user_id } = payload;

    try {
        const messages = await ChatMessages.findAll({
            where: { receiver_id: user_id, read_at: null, deleted_at: null },
            order: [['sent_at', 'ASC']]
        });
        logger.info("[dbManager.getNewMessages] >> messages: ", messages);
        return cb(null, Code.OK, messages);
    } catch (error) {
        logger.error("[dbManager.getNewMessages] >> error: ", error);
        return cb(false, Code.FAIL, error);
    }
}

//7 . viết hàm đánh dấu tất cả tin nhắn đã đọc
exp.markAllMessagesAsRead = async function(payload, cb) {
    logger.info("[dbManager.markAllMessagesAsRead] >> payload: ", payload);
    const { user_id, friend_id } = payload;

    try {
        // Tạo điều kiện cơ bản
        let whereCondition = {
            receiver_id: user_id,
            read_at: null,
            deleted_at: null
        };

        // Nếu có friend_id, thêm điều kiện lọc theo người gửi cụ thể
        if (friend_id) {
            whereCondition.sender_id = friend_id;
            logger.info(`[dbManager.markAllMessagesAsRead] Đánh dấu tin nhắn đã đọc chỉ từ người gửi ${friend_id}`);
        } else {
            logger.info('[dbManager.markAllMessagesAsRead] Đánh dấu tất cả tin nhắn đã đọc');
        }

        // Thực hiện cập nhật
        const result = await ChatMessages.update(
            { read_at: new Date(), is_read: true },
            { where: whereCondition }
        );

        logger.info(`[dbManager.markAllMessagesAsRead] Đã đánh dấu ${result[0]} tin nhắn là đã đọc`);
        return cb(null, Code.OK, {
            success: true,
            messagesMarked: result[0]
        });
    } catch (error) {
        logger.error("[dbManager.markAllMessagesAsRead] >> error: ", error);
        return cb(false, Code.FAIL, error);
    }
}

//8 . viết hàm xoá 1 tin nhắn, nhớ thêm cả sender_id
exp.deleteMessage = async function(payload, cb) {
    logger.info("[dbManager.deleteMessage] >> payload: ", payload);
    const { message_id, sender_id } = payload;

    try {
        await ChatMessages.update({ deleted_at: new Date() }, { where: { id: message_id, sender_id: sender_id } });
        return cb(null, Code.OK, true);
    } catch (error) {
        logger.error("[dbManager.deleteMessage] >> error: ", error);
        return cb(false, Code.FAIL, error);
    }
}

//9 . viết hàm lấy toàn bộ các cuộc trò chuyện (sessions) của 1 user đang có
exp.getChatSessions = async function(payload, cb) {
    logger.info("[dbManager.getChatSessions] >> payload: ", payload);
    const { user_id } = payload;

    try {
        // Fetch all chat sessions for the user
        const sessions = await ChatSessions.findAll({
            where: { [Op.or]: [{ user1_id: user_id }, { user2_id: user_id }] },
            include: [
                {
                    model: Player,
                    as: 'sender',
                    attributes: ['id', 'nick_name', 'avatar']
                },
                {
                    model: Player,
                    as: 'receiver',
                    attributes: ['id', 'nick_name', 'avatar']
                }
            ]
        });

        // Enhance sessions with additional data
        const enhancedSessions = await Promise.all(sessions.map(async (session) => {
            const sessionData = session.toJSON();

            // Get unread message count for this session
            const unreadCount = await ChatMessages.count({
                where: {
                    session_id: session.id,
                    receiver_id: user_id,
                    // is_read: false,
                    read_at: null,
                    deleted_at: null
                }
            });

            // Get last message for this session
            const lastMessage = await ChatMessages.findOne({
                where: {
                    session_id: session.id,
                    deleted_at: null
                },
                order: [['sent_at', 'DESC']],
                attributes: ['id', 'session_id', 'sender_id', 'message', 'is_read', 'read_at', 'sent_at', 'is_system', 'file_url', 'file_type']
            });

            // Add these properties to the session object
            sessionData.unread_count = unreadCount;
            sessionData.last_message = lastMessage ? lastMessage.toJSON() : null;

            return sessionData;
        }));

        // Sort sessions by the last message time (most recent first)
        enhancedSessions.sort((a, b) => {
            const timeA = a.last_message ? new Date(a.last_message.sent_at).getTime() : 0;
            const timeB = b.last_message ? new Date(b.last_message.sent_at).getTime() : 0;
            return timeB - timeA;
        });

        logger.info("[dbManager.getChatSessions] >> enhancedSessions: ", enhancedSessions.length);
        return cb(null, Code.OK, enhancedSessions);
    } catch (error) {
        logger.error("[dbManager.getChatSessions] >> error: ", error);
        return cb(false, Code.FAIL, error);
    }
}

//10 . viết hàm lấy thông tin người chat với mình trong 1 session, trả về là id của người đó
exp.getChatPartner = async function(payload, cb) {
    logger.info("[dbManager.getChatPartner] >> payload: ", payload);
    const { session_id, user_id } = payload;

    try {
        const session = await ChatSessions.findOne({
            where: { id: session_id }
        });

        if (!session) {
            return cb(false, Code.NOT_FOUND, "Session not found");
        }

        // Determine which user is the peer (the other person in the chat)
        const peer_id = session.user1_id === user_id ? session.user2_id : session.user1_id;

        return cb(null, Code.OK, { peer_id });
    } catch (error) {
        logger.error("[dbManager.getChatPartner] >> error: ", error);
        return cb(false, Code.FAIL, error);
    }
}

//11 . viết hàm lọc tin nhắn có file đính kèm, với trạng thái deleted_at is null
exp.filterMessagesWithFile = async function(payload, cb) {
    logger.info("[dbManager.filterMessagesWithFile] >> payload: ", payload);
    const { session_id } = payload;

    try {
        const messages = await ChatMessages.findAll({
            where: { session_id: session_id, file_url: { [Op.not]: null }, deleted_at: null }
        });
        return cb(null, Code.OK, messages);
    } catch (error) {
        logger.error("[dbManager.filterMessagesWithFile] >> error: ", error);
        return cb(false, Code.FAIL, error);
    }
}

//12 . hàm lọc riêng tin nhắn của hệ thống, thêm order by sent_at ASC
exp.filterSystemMessages = async function(payload, cb) {
    logger.info("[dbManager.filterSystemMessages] >> payload: ", payload);
    const { session_id } = payload;

    try {
        const messages = await ChatMessages.findAll({
            where: { session_id: session_id, is_system: true, deleted_at: null },
            order: [['sent_at', 'ASC']]
        });
        return cb(null, Code.OK, messages);
    } catch (error) {
        logger.error("[dbManager.filterSystemMessages] >> error: ", error);
        return cb(false, Code.FAIL, error);
    }
}

//13 . viết hàm đếm số lượng tin nhắn chưa đọc của user theo từng phiên, chú ý phải nhóm (group by) theo session_id
exp.countUnreadMessages = async function(payload, cb) {
    logger.info("[dbManager.countUnreadMessages] >> payload: ", payload);
    const { user_id } = payload;

    try {
        const count = await ChatMessages.findAll({
            where: { receiver_id: user_id, read_at: null, deleted_at: null },
            attributes: ['session_id', [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']],
            group: ['session_id']
        });
        return cb(null, Code.OK, count);
    } catch (error) {
        logger.error("[dbManager.countUnreadMessages] >> error: ", error);
        return cb(false, Code.FAIL, error);
    }
}

//---------------------------------- Badges Management ---------------------------------------------



/**
 * Lấy danh sách thành tích của người chơi
 * @param {Object} payload - Chứa player_id, page, limit, category, offset
 * @param {Function} cb - Callback function
 */
exp.getPlayerBadges = async function(payload, cb) {
    logger.info("[dbManager.getPlayerBadges] >> payload: ", payload);
    const player_id = payload.player_id;
    const page = payload.page || 1;
    const limit = payload.limit || 10;
    const offset = payload.offset || (page - 1) * limit;
    // const category = payload.category;

    const PlayerBadges = models.PlayerBadges;
    const Badges = models.Badges;

    try {
        // Xây dựng điều kiện truy vấn
        const whereCondition = { player_id };
        const badgeWhereCondition = {};

        // if (category) {
        //     badgeWhereCondition.category = category;
        // }

        // Sử dụng findAndCountAll để lấy cả dữ liệu và tổng số bản ghi
        const { count, rows } = await PlayerBadges.findAndCountAll({
            where: whereCondition,
            include: [{
                model: Badges,
                as: 'badge',
                where: Object.keys(badgeWhereCondition).length > 0 ? badgeWhereCondition : undefined
            }],
            order: [['awarded_at', 'DESC']],
            limit: limit,
            offset: offset,
            raw: true,
            nest: true // Để kết quả được lồng nhau thay vì phẳng
        });

        const totalItems = count;
        const totalPages = Math.ceil(totalItems / limit);

        logger.info(`[dbManager.getPlayerBadges] Tìm thấy ${rows.length} thành tích, tổng cộng ${totalItems} thành tích`);

        return cb(null, Code.OK, {
            badges: rows || [],
            totalItems,
            totalPages,
            currentPage: page
        });
    } catch (error) {
        logger.error("[dbManager.getPlayerBadges] Error: ", error);
        return cb(true, Code.FAIL, error.message);
    }
};

/**
 * Lấy thông tin chi tiết của một thành tích của người chơi
 * @param {Object} payload - Chứa badge_id và player_id
 * @param {Function} cb - Callback function
 */
exp.getBadgeDetails = async function(payload, cb) {
    logger.info("[dbManager.getBadgeDetails] >> payload: ", payload);
    const badge_id = payload.badge_id;
    const player_id = payload.player_id;

    if (!badge_id || !player_id) {
        return cb(true, Code.BAD_REQUEST, "Thiếu badge_id hoặc player_id");
    }

    try {
        const PlayerBadges = models.PlayerBadges;
        const Badges = models.Badges;

        // Lấy chi tiết badge và thông tin liên quan của player
        const badgeDetails = await PlayerBadges.findOne({
            where: {
                badge_id: badge_id,
                player_id: player_id
            },
            include: [{
                model: Badges,
                as: 'badge'
            }],
            raw: true,
            nest: true
        });

        if (!badgeDetails) {
            return cb(true, Code.NOT_FOUND, "Không tìm thấy thành tích này");
        }

        logger.info(`[dbManager.getBadgeDetails] Tìm thấy thành tích: ${badgeDetails.badge.name}`);
        return cb(null, Code.OK, badgeDetails);
    } catch (error) {
        logger.error("[dbManager.getBadgeDetails] Error: ", error);
        return cb(true, Code.FAIL, error.message);
    }
};

/**
 * Đánh dấu thành tích đã nhận thưởng
 * @param {Object} payload - Chứa badge_id và player_id
 * @param {Function} cb - Callback function
 */
exp.markBadgeAsClaimed = async function(payload, cb) {
    logger.info("[dbManager.markBadgeAsClaimed] >> payload: ", payload);
    const badge_id = payload.badge_id;
    const player_id = payload.player_id;

    if (!badge_id || !player_id) {
        return cb(true, Code.BAD_REQUEST, "Thiếu badge_id hoặc player_id");
    }

    try {
        const PlayerBadges = models.PlayerBadges;

        // Lấy thông tin player_badge hiện tại
        const playerBadge = await PlayerBadges.findOne({
            where: {
                badge_id: badge_id,
                player_id: player_id
            },
            raw: true
        });

        if (!playerBadge) {
            return cb(true, Code.NOT_FOUND, "Không tìm thấy thành tích này");
        }

        if (playerBadge.claimed_at) {
            return cb(true, Code.FAIL, "Thành tích này đã được nhận thưởng trước đó");
        }

        // Cập nhật trạng thái đã nhận thưởng
        const claimed_at = new Date();

        await PlayerBadges.update(
            { claimed_at: claimed_at },
            {
                where: { id: playerBadge.id }
            }
        );

        logger.info(`[dbManager.markBadgeAsClaimed] Đã đánh dấu thành tích đã nhận thưởng: badge_id=${badge_id}, player_id=${player_id}`);
        return cb(null, Code.OK, {
            ...playerBadge,
            claimed_at
        });
    } catch (error) {
        logger.error("[dbManager.markBadgeAsClaimed] Error: ", error);
        return cb(true, Code.FAIL, error.message);
    }
};

/**
 * Thêm thành tích mới cho người chơi
 * @param {Object} payload - Chứa badge_id, player_id và các thông tin khác
 * @param {Function} cb - Callback function
 */
exp.awardBadgeToPlayer = async function(payload, cb) {
    logger.info("[dbManager.awardBadgeToPlayer] >> payload: ", payload);
    const player_id = payload.player_id;
    const badge_id = payload.badge_id;
    const awarded_at = payload.awarded_at || new Date();

    if (!player_id || !badge_id) {
        return cb(true, Code.BAD_REQUEST, "Thiếu player_id hoặc badge_id");
    }

    try {
        const PlayerBadges = models.PlayerBadges;
        const Badges = models.Badges;

        // Kiểm tra xem badge có tồn tại không
        const badge = await Badges.findOne({
            where: { id: badge_id },
            raw: true
        });

        if (!badge) {
            return cb(true, Code.NOT_FOUND, "Không tìm thấy thành tích này");
        }

        // Kiểm tra xem người chơi này đã có thành tích này chưa
        const existingBadge = await PlayerBadges.findOne({
            where: {
                player_id: player_id,
                badge_id: badge_id
            },
            raw: true
        });

        if (existingBadge) {
            return cb(true, Code.FAIL, "Người chơi đã có thành tích này");
        }

        // Thêm thành tích cho người chơi
        await PlayerBadges.create({
            player_id: player_id,
            badge_id: badge_id,
            awarded_at: awarded_at
        });

        logger.info(`[dbManager.awardBadgeToPlayer] Đã thêm thành tích ${badge.name} cho người chơi ${player_id}`);
        return cb(null, Code.OK, {
            player_id,
            badge_id,
            badge_name: badge.name,
            awarded_at
        });
    } catch (error) {
        logger.error("[dbManager.awardBadgeToPlayer] Error: ", error);
        return cb(true, Code.FAIL, error.message);
    }
};

/**
 * Tạo thành tích mới trong hệ thống
 * @param {Object} payload - Dữ liệu thành tích mới
 * @param {Function} cb - Callback function
 */
exp.createBadge = async function(payload, cb) {
    logger.info("[dbManager.createBadge] >> payload: ", payload);

    // Kiểm tra dữ liệu đầu vào
    if (!payload.name || !payload.code || !payload.description || !payload.category) {
        return cb(true, Code.BAD_REQUEST, "Thiếu thông tin bắt buộc của thành tích");
    }

    try {
        const Badges = models.Badges;

        // Kiểm tra code có trùng lặp không
        const existingBadge = await Badges.findOne({
            where: { code: payload.code },
            raw: true
        });

        if (existingBadge) {
            return cb(true, Code.FAIL, "Mã thành tích đã tồn tại");
        }

        // Tạo thành tích mới
        const newBadge = await Badges.create({
            type: payload.type || null,
            name: payload.name,
            code: payload.code,
            description: payload.description,
            reward: payload.reward || null,
            category: payload.category,
            icon_url: payload.icon_url || null
        });

        logger.info(`[dbManager.createBadge] Đã tạo thành tích mới: ${payload.name}, ID: ${newBadge.id}`);
        return cb(null, Code.OK, {
            id: newBadge.id,
            ...payload
        });
    } catch (error) {
        logger.error("[dbManager.createBadge] Error: ", error);
        return cb(true, Code.FAIL, error.message);
    }
};

/**
 * Lấy danh sách tất cả các thành tích trong hệ thống
 * @param {Object} payload - Chứa các tham số lọc (type)
 * @param {Function} cb - Callback function
 */
exp.getAllBadges = async function(payload, cb) {
    logger.info("[dbManager.getAllBadges] >> payload: ", payload);
    const type = payload.type; // Có thể là POKE_CAREER hoặc ACHIEVEMENTS
    const includeRewards = payload.includeRewards || false;

    try {
        const Badges = models.Badges;
        const BadgeRewards = models.BadgeRewards;

        // Xây dựng điều kiện truy vấn
        const whereCondition = {};

        // Nếu có type, thêm điều kiện lọc theo type
        if (type) {
            whereCondition.type = {
                [Op.in]: Array.isArray(type) ? type : [type]
            };
        }

        // Cấu hình include cho query
        const queryOptions = {
            where: whereCondition,
            order: [
                // ['type', 'ASC'],
                // ['category', 'ASC'],
                ['id', 'ASC']
            ]
        };

        // Nếu yêu cầu bao gồm thông tin phần thưởng
        if (includeRewards) {
            queryOptions.include = [{
                model: BadgeRewards,
                as: 'rewards',
                required: false
            }];
            queryOptions.raw = false;
        } else {
            queryOptions.raw = true;
        }

        // Lấy danh sách badges
        const badges = await Badges.findAll(queryOptions);

        // Xử lý kết quả
        let formattedBadges;
        if (includeRewards) {
            // Chuyển đổi kết quả thành plain objects
            formattedBadges = badges.map(badge => {
                const plainBadge = badge.get({ plain: true });
                return plainBadge;
            });
        } else {
            formattedBadges = badges;
        }

        logger.info(`[dbManager.getAllBadges] Tìm thấy ${formattedBadges.length} thành tích`);

        return cb(null, Code.OK, {
            badges: formattedBadges || []
        });
    } catch (error) {
        logger.error("[dbManager.getAllBadges] Error: ", error);
        return cb(true, Code.FAIL, error.message);
    }
};

/**
 * Lấy danh sách phần thưởng của một thành tích
 * @param {Object} payload - Chứa badge_id
 * @param {Function} cb - Callback function
 */
exp.getBadgeRewards = async function(payload, cb) {
    logger.info("[dbManager.getBadgeRewards] >> payload: ", payload);
    const badge_id = payload.badge_id;

    if (!badge_id) {
        return cb(true, Code.BAD_REQUEST, "Thiếu badge_id");
    }

    try {
        const BadgeRewards = models.BadgeRewards;

        // Lấy danh sách phần thưởng của badge
        const rewards = await BadgeRewards.findAll({
            where: { badge_id: badge_id },
            order: [['id', 'ASC']],
            raw: true
        });

        logger.info(`[dbManager.getBadgeRewards] Tìm thấy ${rewards.length} phần thưởng cho badge_id=${badge_id}`);

        return cb(null, Code.OK, {
            rewards: rewards || []
        });
    } catch (error) {
        logger.error("[dbManager.getBadgeRewards] Error: ", error);
        return cb(true, Code.FAIL, error.message);
    }
};

/**
 * Thêm phần thưởng mới cho thành tích
 * @param {Object} payload - Dữ liệu phần thưởng mới
 * @param {Function} cb - Callback function
 */
exp.createBadgeReward = async function(payload, cb) {
    logger.info("[dbManager.createBadgeReward] >> payload: ", payload);

    // Kiểm tra dữ liệu đầu vào
    if (!payload.badge_id || !payload.reward_type || !payload.reward_value) {
        return cb(true, Code.BAD_REQUEST, "Thiếu thông tin bắt buộc của phần thưởng");
    }

    try {
        const BadgeRewards = models.BadgeRewards;
        const Badges = models.Badges;

        // Kiểm tra badge có tồn tại không
        const badge = await Badges.findOne({
            where: { id: payload.badge_id },
            raw: true
        });

        if (!badge) {
            return cb(true, Code.NOT_FOUND, "Không tìm thấy thành tích này");
        }

        // Tạo phần thưởng mới
        const newReward = await BadgeRewards.create({
            badge_id: payload.badge_id,
            reward_type: payload.reward_type,
            reward_value: payload.reward_value,
            is_permanent: payload.is_permanent || false
        });

        logger.info(`[dbManager.createBadgeReward] Đã tạo phần thưởng mới cho badge_id=${payload.badge_id}`);
        return cb(null, Code.OK, newReward);
    } catch (error) {
        logger.error("[dbManager.createBadgeReward] Error: ", error);
        return cb(true, Code.FAIL, error.message);
    }
};

//---------------------------- Level Ranks ------------------------------------------------------------

/**
 * Lấy danh sách tất cả các cấp độ
 * @param {Object} payload - Chứa các tham số lọc (nếu có)
 * @param {Function} cb - Callback function
 */
exp.getAllLevelRanks = async function(payload, cb) {
    try {
        const levelRanks = await LevelRank.findAll({
            order: [['level', 'ASC']]
        });

        logger.info(`[dbManager.getAllLevelRanks] Tìm thấy ${levelRanks.length} cấp độ`);

        return cb(null, Code.OK, {
            levelRanks: levelRanks || []
        });
    } catch (error) {
        logger.error("[dbManager.getAllLevelRanks] Error: ", error);
        return cb(true, Code.FAIL, error.message);
    }
};

/**
 * Lấy thông tin cấp độ theo level
 * @param {Object} payload - Chứa level
 * @param {Function} cb - Callback function
 */
exp.getLevelRankByLevel = async function(payload, cb) {
    const { level } = payload;

    if (!level) {
        return cb(true, Code.BAD_REQUEST, "Missing level parameter");
    }

    try {
        const levelRank = await LevelRank.findOne({
            where: { level: level }
        });

        if (!levelRank) {
            return cb(null, Code.NOT_FOUND, null);
        }

        logger.info(`[dbManager.getLevelRankByLevel] Tìm thấy cấp độ ${level}`);

        return cb(null, Code.OK, levelRank);
    } catch (error) {
        logger.error("[dbManager.getLevelRankByLevel] Error: ", error);
        return cb(true, Code.FAIL, error.message);
    }
};

/**
 * Lấy thông tin cấp độ tiếp theo của người chơi
 * @param {Object} payload - Chứa currentLevel
 * @param {Function} cb - Callback function
 */
exp.getNextLevelRank = async function(payload, cb) {
    const { currentLevel } = payload;

    if (!currentLevel) {
        return cb(true, Code.BAD_REQUEST, "Missing currentLevel parameter");
    }

    try {
        const nextLevel = parseInt(currentLevel) + 1;
        const levelRank = await LevelRank.findOne({
            where: { level: nextLevel }
        });

        if (!levelRank) {
            return cb(null, Code.NOT_FOUND, {
                message: "Đã đạt cấp độ tối đa"
            });
        }

        logger.info(`[dbManager.getNextLevelRank] Tìm thấy cấp độ tiếp theo ${nextLevel}`);

        return cb(null, Code.OK, levelRank);
    } catch (error) {
        logger.error("[dbManager.getNextLevelRank] Error: ", error);
        return cb(true, Code.FAIL, error.message);
    }
};

//---------------------------- Begin: Shop Items ------------------------------------------------------------
/**
 * Lấy danh sách vật phẩm trong shop
 * @param {Object} payload - Chứa các tham số lọc (type, category, currency)
 * @param {Function} cb - Callback function
 */
exp.getShopItems = async function(payload, cb) {
    logger.info('[dbManager.getShopItems] payload:', payload);

    try {
        // Xây dựng điều kiện truy vấn
        const whereCondition = {
            status: 1 // Chỉ lấy các vật phẩm có trạng thái khả dụng
        };

        if (payload.type) {
            whereCondition.type = payload.type;
        }

        if (payload.category) {
            whereCondition.category = payload.category;
        }

        if (payload.currency) {
            whereCondition.currency = payload.currency;
        }

        // Truy vấn danh sách vật phẩm
        const shopItems = await ShopItems.findAll({
            where: whereCondition,
            order: [
                ['type', 'ASC'],
                ['price', 'ASC']
            ]
        });

        logger.info('[dbManager.getShopItems] Found items:', shopItems.length);

        // Chuyển đổi sang DTO để loại bỏ created_at và updated_at
        const dtoItems = ShopItemDTO.fromList(shopItems);

        return cb(null, Code.OK, dtoItems);
    } catch (error) {
        logger.error('[dbManager.getShopItems] Error:', error);
        return cb(null, Code.FAIL, error.message);
    }
};

/**
 * Lấy thông tin chi tiết một vật phẩm trong shop
 * @param {Object} payload - Chứa item_id
 * @param {Function} cb - Callback function
 */
exp.getShopItemDetail = async function(payload, cb) {
    logger.info('[dbManager.getShopItemDetail] payload:', payload);

    try {
        if (!payload.item_id) {
            return cb(null, Code.BAD_REQUEST, 'Item ID is required');
        }

        // Truy vấn thông tin vật phẩm
        const shopItem = await ShopItems.findOne({
            where: {
                id: payload.item_id,
                status: 1 // Chỉ lấy vật phẩm có trạng thái khả dụng
            }
        });

        if (!shopItem) {
            return cb(null, Code.NOT_FOUND, 'Item not found');
        }

        logger.info('[dbManager.getShopItemDetail] Found item:', shopItem.id);

        // Chuyển đổi sang DTO để loại bỏ created_at và updated_at
        const dtoItem = new ShopItemDTO(shopItem);

        return cb(null, Code.OK, dtoItem);
    } catch (error) {
        logger.error('[dbManager.getShopItemDetail] Error:', error);
        return cb(null, Code.FAIL, error.message);
    }
};

/**
 * Mua vật phẩm trong shop
 * @param {Object} payload - Chứa player_id và item_id
 * @param {Function} cb - Callback function
 */
exp.buyItem = async function(payload, cb) {
    logger.info('[dbManager.buyItem] payload:', payload);

    const transaction = await sequelize.transaction();

    try {
        if (!payload.player_id || !payload.item_id) {
            await transaction.rollback();
            return cb(null, Code.BAD_REQUEST, 'Player ID and Item ID are required');
        }

        // Lấy thông tin vật phẩm
        const shopItem = await ShopItems.findOne({
            where: {
                id: payload.item_id,
                status: 1 // Chỉ lấy vật phẩm có trạng thái khả dụng
            },
            transaction
        });

        if (!shopItem) {
            await transaction.rollback();
            return cb(null, Code.NOT_FOUND, 'Item not found');
        }

        // Lấy thông tin người chơi
        const player = await Player.findOne({
            where: {
                id: payload.player_id
            },
            transaction
        });

        if (!player) {
            await transaction.rollback();
            return cb(null, Code.NOT_FOUND, 'Player not found');
        }

        // Kiểm tra số dư của người chơi
        const currency = shopItem.currency.toLowerCase();
        let playerBalance = 0;

        switch (currency) {
            case 'chip':
                playerBalance = player.chip || 0;
                break;
            case 'coin':
                playerBalance = player.coin || 0;
                break;
            case 'diamond':
                playerBalance = player.diamond || 0;
                break;
            default:
                await transaction.rollback();
                return cb(null, Code.BAD_REQUEST, 'Unsupported currency');
        }

        if (playerBalance < shopItem.price) {
            await transaction.rollback();
            return cb(null, Code.BAD_REQUEST, 'Insufficient balance');
        }

        // Trừ tiền người chơi
        const updateData = {};
        updateData[currency] = Sequelize.literal(`${currency} - ${shopItem.price}`);

        await Player.update(updateData, {
            where: {
                id: payload.player_id
            },
            transaction
        });

        // Cộng giá trị vật phẩm cho người chơi
        const itemType = shopItem.type.toLowerCase();
        const addItemData = {};

        switch (itemType) {
            case 'chip':
                addItemData.chip = Sequelize.literal(`chip + ${shopItem.item_value}`);
                break;
            case 'coin':
                addItemData.coin = Sequelize.literal(`coin + ${shopItem.item_value}`);
                break;
            case 'diamond':
                addItemData.diamond = Sequelize.literal(`diamond + ${shopItem.item_value}`);
                break;
            case 'props':
                // Xử lý đạo cụ
                if (shopItem.category === 'PT') {
                    addItemData.pt_item = Sequelize.literal(`pt_item + ${shopItem.item_value}`);
                } else if (shopItem.category === 'BUBBLE_CHAT') {
                    addItemData.bubble_chat = Sequelize.literal(`bubble_chat + ${shopItem.item_value}`);
                }
                break;
            case 'card':
                // Xử lý thẻ VIP
                addItemData.vip_point = Sequelize.literal(`vip_point + ${shopItem.item_value}`);
                break;
            default:
                // Các loại vật phẩm khác có thể xử lý sau
                break;
        }

        if (Object.keys(addItemData).length > 0) {
            await Player.update(addItemData, {
                where: {
                    id: payload.player_id
                },
                transaction
            });
        }

        // Lấy thông tin người chơi sau khi cập nhật
        const updatedPlayer = await Player.findOne({
            where: {
                id: payload.player_id
            },
            transaction
        });

        // Tạo đối tượng balance để trả về
        const balance = {
            chip: updatedPlayer.chip || 0,
            coin: updatedPlayer.coin || 0,
            diamond: updatedPlayer.diamond || 0
        };

        // Thêm các trường khác nếu cần
        if (updatedPlayer.pt_item) balance.pt_item = updatedPlayer.pt_item;
        if (updatedPlayer.bubble_chat) balance.bubble_chat = updatedPlayer.bubble_chat;
        if (updatedPlayer.vip_point) balance.vip_point = updatedPlayer.vip_point;

        // Ghi log giao dịch
        const logData = {
            player_id: payload.player_id,
            action: 'BUY_ITEM',
            item_id: shopItem.id,
            item_name: shopItem.item_name,
            price: shopItem.price,
            currency: shopItem.currency,
            item_value: shopItem.item_value,
            item_type: shopItem.type,
            created_at: new Date()
        };

        // Có thể thêm code để lưu log vào bảng logs nếu cần

        await transaction.commit();

        logger.info('[dbManager.buyItem] Purchase successful:', logData);

        // Chuyển đổi sang DTO để loại bỏ created_at và updated_at
        const dtoItem = new ShopItemDTO(shopItem);

        return cb(null, Code.OK, {
            balance: balance,
            item: dtoItem
        });
    } catch (error) {
        await transaction.rollback();
        logger.error('[dbManager.buyItem] Error:', error);
        return cb(null, Code.FAIL, error.message);
    }
};
/**
 * Lấy lịch sử mua vật phẩm của người chơi
 * @param {Object} payload - Chứa player_id và các tham số lọc
 * @param {Function} cb - Callback function
 */
exp.getShopPurchaseLogs = async function(payload, cb) {
    logger.info('[dbManager.getShopPurchaseLogs] payload:', payload);

    try {
        if (!payload.player_id) {
            return cb(null, Code.BAD_REQUEST, 'Player ID is required');
        }

        // Xây dựng điều kiện truy vấn
        const whereCondition = {
            player_id: payload.player_id
        };

        if (payload.shop_item_id) {
            whereCondition.shop_item_id = payload.shop_item_id;
        }

        if (payload.type) {
            whereCondition.type = payload.type;
        }

        if (payload.category) {
            whereCondition.category = payload.category;
        }

        if (payload.order_status) {
            whereCondition.order_status = payload.order_status;
        }

        // Truy vấn lịch sử mua vật phẩm
        const purchaseLogs = await ShopPurchaseLogs.findAll({
            where: whereCondition,
            order: [['purchased_at', 'DESC']],
            limit: payload.limit || 50,
            offset: payload.offset || 0
        });

        logger.info('[dbManager.getShopPurchaseLogs] Found logs:', purchaseLogs.length);

        // Chuyển đổi sang DTO để loại bỏ created_at và updated_at
        const dtoLogs = ShopPurchaseLogDTO.fromList(purchaseLogs);

        return cb(null, Code.OK, dtoLogs);
    } catch (error) {
        logger.error('[dbManager.getShopPurchaseLogs] Error:', error, ' -> message: ', error.message, ' -> stack: ', error.stack);
        return cb(null, Code.FAIL, error.message);
    }
};

/**
 * Tạo log mua vật phẩm
 * @param {Object} payload - Dữ liệu log
 * @param {Function} cb - Callback function
 */
exp.createShopPurchaseLog = async function(payload, cb) {
    logger.info('[dbManager.createShopPurchaseLog] payload:', payload);

    try {
        if (!payload.player_id || !payload.shop_item_id) {
            return cb(null, Code.BAD_REQUEST, 'Player ID and Shop Item ID are required');
        }

        const purchaseLog = await ShopPurchaseLogs.create(payload);

        logger.info('[dbManager.createShopPurchaseLog] Log created:', purchaseLog.id);

        // Chuyển đổi sang DTO để loại bỏ created_at và updated_at
        const dtoLog = new ShopPurchaseLogDTO(purchaseLog);

        return cb(null, Code.OK, dtoLog);
    } catch (error) {
        logger.error('[dbManager.createShopPurchaseLog] Error:', error);
        return cb(null, Code.FAIL, error.message);
    }
};
//---------------------------- End: Shop Items ------------------------------------------------------------

//---------------------------- Begin: SNG Tournaments ------------------------------------------------------------
/**
 * Lấy số lượng người chơi đã đăng ký cho từng loại bàn SNG
 * @param {Function} cb - Callback function
 */
exp.getRegisteredPlayersCount = async function(cb) {
    logger.info('[dbManager.getRegisteredPlayersCount]');
    
    try {
        // Query để lấy số lượng người chơi đã đăng ký theo table_type và level
        const result = await sequelize.query(`
            SELECT 
                CASE 
                    WHEN st.player_capacity = 5 THEN '5_PLAYERS'
                    WHEN st.player_capacity = 9 THEN '9_PLAYERS'
                END as table_type,
                JSON_UNQUOTE(JSON_EXTRACT(st.metadata, '$.level')) as level,
                COUNT(stp.id) as registered_count
            FROM sng_tournaments st
            LEFT JOIN sng_tournament_players stp ON st.id = stp.tournament_id AND stp.status = 'ACTIVE'
            WHERE st.status IN ('WAITING', 'READY')
                AND st.metadata IS NOT NULL
                AND JSON_EXTRACT(st.metadata, '$.level') IS NOT NULL
            GROUP BY st.player_capacity, JSON_UNQUOTE(JSON_EXTRACT(st.metadata, '$.level'))
        `, {
            type: QueryTypes.SELECT
        });
        
        // Convert result to key-value format
        const counts = {};
        result.forEach(row => {
            if (row.table_type && row.level) {
                const key = row.table_type + '_' + row.level;
                counts[key] = row.registered_count || 0;
            }
        });
        
        logger.info('[dbManager.getRegisteredPlayersCount] Counts:', counts);
        return cb(null, Code.OK, counts);
    } catch (error) {
        logger.error('[dbManager.getRegisteredPlayersCount] Error:', error);
        return cb(null, Code.FAIL, error.message);
    }
};

/**
 * Tìm bàn SNG còn trống để thêm người chơi
 * @param {Object} payload - Chứa table_type và level
 * @param {Function} cb - Callback function
 */
exp.findAvailableSngTable = async function(payload, cb) {
    logger.info('[dbManager.findAvailableSngTable] payload:', payload);
    
    try {
        if (!payload.table_type || !payload.level) {
            return cb(null, Code.BAD_REQUEST, 'Table type and level are required');
        }
        
        const playerCapacity = payload.table_type === '5_PLAYERS' ? 5 : 9;
        
        // Tìm bàn còn trống
        const tournament = await sequelize.query(`
            SELECT st.*, COUNT(stp.id) as current_players
            FROM sng_tournaments st
            LEFT JOIN sng_tournament_players stp ON st.id = stp.tournament_id AND stp.status = 'ACTIVE'
            WHERE st.status IN ('WAITING', 'READY')
                AND st.player_capacity = :playerCapacity
                AND JSON_UNQUOTE(JSON_EXTRACT(st.metadata, '$.level')) = :level
            GROUP BY st.id
            HAVING current_players < st.player_capacity
            ORDER BY st.created_at ASC
            LIMIT 1
        `, {
            replacements: { 
                playerCapacity: playerCapacity,
                level: payload.level
            },
            type: QueryTypes.SELECT
        });
        
        if (tournament.length > 0) {
            logger.info('[dbManager.findAvailableSngTable] Found available table:', tournament[0].id);
            return cb(null, Code.OK, tournament[0]);
        } else {
            logger.info('[dbManager.findAvailableSngTable] No available table found');
            return cb(null, Code.NOT_FOUND, null);
        }
    } catch (error) {
        logger.error('[dbManager.findAvailableSngTable] Error:', error);
        return cb(null, Code.FAIL, error.message);
    }
};

/**
 * Tạo bàn SNG mới
 * @param {Object} payload - Dữ liệu bàn mới
 * @param {Function} cb - Callback function
 */
// exp.createSngTournament = async function(payload, cb) {
//     logger.info('[dbManager.createSngTournament] payload:', payload);
    
//     const transaction = await sequelize.transaction();
    
//     try {
//         if (!payload.player_capacity || !payload.buy_in || !payload.fee) {
//             await transaction.rollback();
//             return cb(null, Code.BAD_REQUEST, 'Player capacity, buy_in and fee are required');
//         }
        
//         // Tạo mã tournament duy nhất
//         const tournamentCode = `SNG_${payload.player_capacity}_${payload.level}_${Date.now()}`;
        
//         const metadata = {
//             level: payload.level,
//             table_type: payload.table_type,
//             initial_chips: payload.initial_chips || 100000000,
//             blind_duration_minutes: payload.blind_duration_minutes || 5
//         };
        
//         const tournament = await models.SngTournament.create({
//             code: tournamentCode,
//             status: 'WAITING',
//             player_capacity: payload.player_capacity,
//             buy_in: payload.buy_in,
//             fee: payload.fee,
//             reward_pool: payload.buy_in * payload.player_capacity,
//             metadata: JSON.stringify(metadata)
//         }, { transaction });
        
//         await transaction.commit();
        
//         logger.info('[dbManager.createSngTournament] Tournament created:', tournament.id);
//         return cb(null, Code.OK, tournament);
//     } catch (error) {
//         await transaction.rollback();
//         logger.error('[dbManager.createSngTournament] Error:', error);
//         return cb(null, Code.FAIL, error.message);
//     }
// };

/**
 * Đăng ký người chơi vào giải đấu SNG
 * @param {Object} payload - Chứa tournament_id, player_id và thông tin khác
 * @param {Function} cb - Callback function
 */
exp.registerPlayerToSngTournament = async function(payload, cb) {
    logger.info('[dbManager.registerPlayerToSngTournament] payload:', payload);
    
    const transaction = await sequelize.transaction();
    
    try {
        if (!payload.tournament_id || !payload.player_id) {
            await transaction.rollback();
            return cb(null, Code.BAD_REQUEST, 'Tournament ID and Player ID are required');
        }
        
        // Kiểm tra người chơi đã đăng ký chưa
        const existingRegistration = await models.SngTournamentPlayer.findOne({
            where: {
                tournament_id: payload.tournament_id,
                player_id: payload.player_id
            },
            transaction
        });
        
        if (existingRegistration) {
            await transaction.rollback();
            return cb(null, Code.BAD_REQUEST, 'Player already registered for this tournament');
        }
        
        // Đăng ký người chơi
        const registration = await models.SngTournamentPlayer.create({
            tournament_id: payload.tournament_id,
            player_id: payload.player_id,
            initial_chips: payload.initial_chips || 100000000,
            current_chips: payload.initial_chips || 100000000,
            status: 'ACTIVE'
        }, { transaction });
        
        // Kiểm tra xem đã đủ người chưa
        const currentPlayers = await models.SngTournamentPlayer.count({
            where: {
                tournament_id: payload.tournament_id,
                status: 'ACTIVE'
            },
            transaction
        });
        
        const tournament = await models.SngTournament.findByPk(payload.tournament_id, { transaction });
        
        let allPlayerIds = [];
        if (currentPlayers >= tournament.player_capacity) {
            // Cập nhật trạng thái tournament thành READY
            await tournament.update({
                status: 'READY'
            }, { transaction });
            
            // Get all player IDs for this tournament
            const allRegistrations = await models.SngTournamentPlayer.findAll({
                where: {
                    tournament_id: payload.tournament_id,
                    status: 'ACTIVE'
                },
                attributes: ['player_id'],
                transaction
            });
            
            allPlayerIds = allRegistrations.map(reg => reg.player_id);
        }
        
        await transaction.commit();
        
        logger.info('[dbManager.registerPlayerToSngTournament] Player registered:', registration.id);
        return cb(null, Code.OK, {
            registration: registration,
            current_players: currentPlayers,
            is_ready: currentPlayers >= tournament.player_capacity,
            all_player_ids: allPlayerIds
        });
    } catch (error) {
        await transaction.rollback();
        logger.error('[dbManager.registerPlayerToSngTournament] Error:', error);
        return cb(null, Code.FAIL, error.message);
    }
};

/**
 * Cập nhật số dư của người chơi
 * @param {Object} playerId - ID người chơi
 * @param {Object} amount - Số tiền thay đổi (âm để trừ, dương để cộng)
 * @param {Object} description - Mô tả giao dịch
 * @param {Function} cb - Callback function
 */
exp.updatePlayerBalance = async function(playerId, amount, description, cb) {
    logger.info('[dbManager.updatePlayerBalance] playerId:', playerId, 'amount:', amount);
    
    const transaction = await sequelize.transaction();
    
    try {
        // Cập nhật balance của player
        const player = await Player.findByPk(playerId, { transaction });
        
        if (!player) {
            await transaction.rollback();
            return cb(null, Code.NOT_FOUND, 'Player not found');
        }
        
        const newBalance = (player.balance || 0) + amount;
        
        if (newBalance < 0) {
            await transaction.rollback();
            return cb(null, Code.BAD_REQUEST, 'Insufficient balance');
        }
        
        await player.update({
            balance: newBalance
        }, { transaction });
        
        // Có thể thêm log transaction ở đây nếu cần
        
        await transaction.commit();
        
        logger.info('[dbManager.updatePlayerBalance] Balance updated. New balance:', newBalance);
        return cb(null, Code.OK, { 
            old_balance: player.balance,
            new_balance: newBalance,
            amount: amount
        });
    } catch (error) {
        await transaction.rollback();
        logger.error('[dbManager.updatePlayerBalance] Error:', error);
        return cb(null, Code.FAIL, error.message);
    }
};

/**
 * Lấy thông tin giải đấu SNG theo ID
 * @param {number} tournamentId - ID giải đấu
 * @param {Function} cb - Callback function
 */
exp.getSngTournamentById = async function(tournamentId, cb) {
    logger.info('[dbManager.getSngTournamentById] tournamentId:', tournamentId);

    try {
        const tournament = await models.SngTournament.findByPk(tournamentId);

        if (!tournament) {
            logger.info('[dbManager.getSngTournamentById] Tournament not found:', tournamentId);
            return cb(null, Code.NOT_FOUND, null);
        }

        logger.info('[dbManager.getSngTournamentById] Tournament found:', tournament.id);
        return cb(null, Code.OK, tournament);
    } catch (error) {
        logger.error('[dbManager.getSngTournamentById] Error:', error);
        return cb(null, Code.FAIL, error.message);
    }
};

/**
 * Đếm số người chơi còn lại (ACTIVE) trong giải đấu SNG
 * @param {number} tournamentId - ID giải đấu
 * @param {Function} cb - Callback function
 */
exp.countActiveSngTournamentPlayers = async function(tournamentId, cb) {
    logger.info('[dbManager.countActiveSngTournamentPlayers] tournamentId:', tournamentId);

    try {
        const activeCount = await models.SngTournamentPlayer.count({
            where: {
                tournament_id: tournamentId,
                status: 'ACTIVE'
            }
        });

        logger.info('[dbManager.countActiveSngTournamentPlayers] Active players count:', activeCount);
        return cb(null, Code.OK, activeCount);
    } catch (error) {
        logger.error('[dbManager.countActiveSngTournamentPlayers] Error:', error);
        return cb(null, Code.FAIL, error.message);
    }
};

/**
 * Note: có 1 bản clone tươnt tự bê dbService
 * Cập nhật trạng thái người chơi trong giải đấu SNG
 * @param {Object} payload - Dữ liệu cập nhật
 * @param {Function} cb - Callback function
 */
exp.updateSngTournamentPlayer = async function(payload, cb) {
    logger.info('[dbManager.updateSngTournamentPlayer] payload:', payload);

    try {
        const { tournament_id, player_id, status, rank, current_chips, eliminated_at_hand } = payload;

        const [affectedRows] = await models.SngTournamentPlayer.update({
            status: status,
            rank: rank,
            current_chips: current_chips,
            eliminated_at_hand: eliminated_at_hand
        }, {
            where: {
                tournament_id: tournament_id,
                player_id: player_id
            }
        });

        if (affectedRows > 0) {
            logger.info('[dbManager.updateSngTournamentPlayer] Player updated successfully');
            return cb(null, Code.OK, { affected_rows: affectedRows });
        } else {
            logger.warn('[dbManager.updateSngTournamentPlayer] No player found to update');
            return cb(null, Code.NOT_FOUND, null);
        }
    } catch (error) {
        logger.error('[dbManager.updateSngTournamentPlayer] Error:', error);
        return cb(null, Code.FAIL, error.message);
    }
};

/**
 * Note: có 1 bản clone tươnt tự bê dbService
 * Ghi log hành động trong giải đấu SNG
 * @param {Object} payload - Dữ liệu log
 * @param {Function} cb - Callback function
 */
exp.createSngTournamentLog = async function(payload, cb) {
    logger.info('[dbManager.createSngTournamentLog] payload:', payload);

    try {
        const { tournament_id, player_id, action_type, data, amount } = payload;

        const log = await models.SngTournamentLog.create({
            tournament_id: tournament_id,
            player_id: player_id,
            action_type: action_type,
            data: data,
            amount: amount
        });

        logger.info('[dbManager.createSngTournamentLog] Log created successfully:', log.id);
        return cb(null, Code.OK, log);
    } catch (error) {
        logger.error('[dbManager.createSngTournamentLog] Error:', error);
        return cb(null, Code.FAIL, error.message);
    }
};

/**
 * Note: có 1 bản clone tươnt tự bên dbService
 * Cập nhật trạng thái giải đấu SNG
 * @param {Object} payload - Dữ liệu cập nhật
 * @param {Function} cb - Callback function
 */
exp.updateSngTournament = async function(payload, cb) {
    logger.info('[dbManager.updateSngTournament] payload:', payload);

    try {
        const { tournament_id, status, started_at, ended_at } = payload;

        const updateData = { status };
        if (started_at) updateData.started_at = started_at;
        if (ended_at) updateData.ended_at = ended_at;

        const [affectedRows] = await models.SngTournament.update(updateData, {
            where: { id: tournament_id }
        });

        if (affectedRows > 0) {
            logger.info('[dbManager.updateSngTournament] Tournament updated successfully');
            return cb(null, Code.OK, { affected_rows: affectedRows });
        } else {
            logger.warn('[dbManager.updateSngTournament] No tournament found to update');
            return cb(null, Code.NOT_FOUND, null);
        }
    } catch (error) {
        logger.error('[dbManager.updateSngTournament] Error:', error);
        return cb(null, Code.FAIL, error.message);
    }
};

/**
 * Note: có 1 bản clone tươnt tự bên dbService
 * Tạo tournament trong database
 * @param {Object} payload - Dữ liệu tournament
 * @param {Function} cb - Callback function
 */
exp.createSngTournament = async function(payload, cb) {
    logger.info('[dbManager.createSngTournament] payload:', payload);

    try {
        const tournament = await models.SngTournament.create(payload);

        logger.info('[dbManager.createSngTournament] Tournament created successfully:', tournament.id);
        return cb(null, Code.OK, tournament);
    } catch (error) {
        logger.error('[dbManager.createSngTournament] Error:', error);
        return cb(null, Code.FAIL, error.message);
    }
};

/** 
 * Note: có 1 bản clone tươnt tự bên dbService
 * Tạo tournament player trong database
 * @param {Object} payload - Dữ liệu tournament player
 * @param {Function} cb - Callback function
 */
exp.createSngTournamentPlayer = async function(payload, cb) {
    logger.info('[dbManager.createSngTournamentPlayer] payload:', payload);

    try {
        const tournamentPlayer = await models.SngTournamentPlayer.create(payload);

        logger.info('[dbManager.createSngTournamentPlayer] Tournament player created successfully:', tournamentPlayer.id);
        return cb(null, Code.OK, tournamentPlayer);
    } catch (error) {
        logger.error('[dbManager.createSngTournamentPlayer] Error:', error);
        return cb(null, Code.FAIL, error.message);
    }
};
//---------------------------- End: SNG Tournaments ------------------------------------------------------------
