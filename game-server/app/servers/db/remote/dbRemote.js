// const logger = require('pomelo-logger/lib/logger');
// var logger      = require('pomelo-logger').getLogger('db-log', __filename);
var logger      = require('pomelo-logger').getLogger(__filename);
var dbm 		= require('../../../controllers/dbManager');

module.exports = function(app) {
	return new DBRemote(app);
};

var DBRemote = function(app) {
	this.app = app;
};

//----------------------------------------  RPC Remote Function ----- -------------------------------------------

//---------------------------- Player Data ------------------------------------------------------------
DBRemote.prototype.getPlayerData = function(userID, cb) {
	dbm.getPlayerData(userID,cb);
};
DBRemote.prototype.createPlayer = function(payload, cb) {
	dbm.createPlayer(payload, cb);
};
DBRemote.prototype.getPlayerByUserId = function(userID, cb) {
	logger.info('getPlayerByUserId >> userID:', userID);
	dbm.getPlayerByUid(userID, cb);
};

DBRemote.prototype.getPlayerById = function(id, cb) {
	logger.info('getPlayerById >> id:', id);
	dbm.getPlayerById(id, cb);
};

DBRemote.prototype.getPlayersByIds = function(ids, cb){
	dbm.getPlayersByIds(ids, cb);
};

DBRemote.prototype.updateLastLogin = function(userID, cb) {
	const lastLogin  = Math.floor(Date.now() / 1000);
	logger.info('[dbRemote.updateLastLogin] lastLogin: ', lastLogin, ' -> userID: ', userID);
	dbm.updatePlayer(userID, { last_login: lastLogin }, cb);
};

DBRemote.prototype.updatePlayer = function(playerId, payload, cb) {
	logger.info('[dbRemote.updatePlayer] playerId: ', playerId, ' -> payload: ', payload);
	dbm.updatePlayer(playerId, payload, cb);
};

DBRemote.prototype.incrementPlayerStats = function(playerId, payload, cb) {
	logger.info('[dbRemote.incrementPlayerStats] playerId: ', playerId, ' -> payload: ', payload);
	dbm.incrementPlayerStats(playerId, payload, cb);
};

//---------------------------- Properties Data ------------------------------------------------------------
DBRemote.prototype.getPropertyById = function(userID, cb) {
	dbm.getPropertyById(userID, cb);
};

DBRemote.prototype.getPropertiesByIds = function(uids, cb){
	dbm.getPropertiesByIds(uids, cb);
};

DBRemote.prototype.updateProperties = function(userID, msg, cb) {
	dbm.updateProperties(userID, msg, cb);
};

//---------------------------- Logs Data ------------------------------------------------------------
// đây là hàm tạo logs game trong ván đánh
DBRemote.prototype.createLogs = function(users, data, cb) {
	dbm.createLogs(users, data, cb);
};

DBRemote.prototype.createLidUid = function(userId, lid, cb) {
	dbm.createLidUid(userId, lid, cb);
};

DBRemote.prototype.searchPlayer = function(payload, cb) {
	dbm.searchPlayer(payload, cb);
};

//---------------------------- Friends Data ------------------------------------------------------------
DBRemote.prototype.addFriend = function(data, cb) {
	dbm.addFriend(data, cb);
};

DBRemote.prototype.updateFriendType = function(data, cb) {
	dbm.updateFriendType(data, cb);
};

DBRemote.prototype.checkFriendExist = function(payload, cb) {
	dbm.checkFriendExist(payload, cb);
};

DBRemote.prototype.deleteFriend = function(payload, cb) {
	dbm.deleteFriend(payload, cb);
};

DBRemote.prototype.destroyItemFriendWithWhereParams = function(payload, cb) {
	dbm.destroyItemFriendWithWhereParams(payload, cb);
};

// Get bạn bè (có phân trang)
DBRemote.prototype.getFriends = function(payload, cb) {
	dbm.getFriends(payload, cb);
};

DBRemote.prototype.searchFriends = function(payload, cb) {
	dbm.searchFriends(payload, cb);
};

// Get tất cả bạn bè không phân trang
DBRemote.prototype.getAllFriends = function(payload, cb) {
	dbm.getAllFriends(payload, cb);
};

DBRemote.prototype.getFriendsByTypes = function(payload, cb) {
	dbm.getFriendsByTypes(payload, cb);
};

// Danh sách những người đã gửi lời mời kết bạn đến mình (có phân trang)
DBRemote.prototype.getInviteFriends = function(payload, cb) {
	dbm.getInviteFriends(payload, cb);
};

// danh sách những người mình đã gửi lời kết bạn mà họ chưa có action gì 
DBRemote.prototype.getFollowFriends = function(payload, cb) {
	dbm.getFollowFriends(payload, cb);
};

// Danh sách người chơi bị chặn (danh sách đen) (có phân trang)
DBRemote.prototype.getBlockFriends = function(payload, cb) {
	dbm.getBlockFriends(payload, cb);
};

DBRemote.prototype.getRandomPlayersNotFriends = function(payload, cb) {
	dbm.getRandomPlayersNotFriends(payload, cb);
};

//---------------------------- Mini Lucky Cards ------------------------------------------------------------
DBRemote.prototype.getMiniLuckyCard = function(payload, cb) {
	dbm.getMiniLuckyCard(payload, cb);
};

//---------------------------- Begin: Chat Sessions ------------------------------------------------------------
// 1
DBRemote.prototype.checkChatSession = function(payload, cb) {
	dbm.checkChatSession(payload, cb);
};

// 2
DBRemote.prototype.createChatSession = function(payload, cb) {
	dbm.createChatSession(payload, cb);
};

// 3
DBRemote.prototype.createMessage = function(payload, cb) {
	dbm.createMessage(payload, cb);
};

// DBRemote.prototype.getMessages = function(payload, cb) {
// 	dbm.getMessages(payload, cb);
// };

// 4
DBRemote.prototype.createBotMessage = function(payload, cb) {
	dbm.createBotMessage(payload, cb);
};

// 5
DBRemote.prototype.getChatHistory = function(payload, cb) {
	dbm.getChatHistory(payload, cb);
};

// 6
DBRemote.prototype.getNewMessages = function(payload, cb) {
	dbm.getNewMessages(payload, cb);
};

// 7
DBRemote.prototype.markAllMessagesAsRead = function(payload, cb) {
	dbm.markAllMessagesAsRead(payload, cb);
};

// 8
DBRemote.prototype.deleteMessage = function(payload, cb) {
	dbm.deleteMessage(payload, cb);
};

// 9
DBRemote.prototype.getChatSessions = function(payload, cb) {
	dbm.getChatSessions(payload, cb);
};

// 10
DBRemote.prototype.getChatPartner = function(payload, cb) {
	dbm.getChatPartner(payload, cb);
};

// 11
DBRemote.prototype.filterMessagesWithFile = function(payload, cb) {
	dbm.filterMessagesWithFile(payload, cb);
};

// 12
DBRemote.prototype.filterSystemMessages = function(payload, cb) {
	dbm.filterSystemMessages(payload, cb);
};

// 13
DBRemote.prototype.countUnreadMessages = function(payload, cb) {
	dbm.countUnreadMessages(payload, cb);
};
//---------------------------- End: Chat Sessions ------------------------------------------------------------

//---------------------------- Begin: Badges ------------------------------------------------------------
/**
 * Lấy danh sách thành tích của người chơi
 * @param {Object} payload - Chứa player_id, page, limit, category
 * @param {Function} cb - Callback function
 */
DBRemote.prototype.getPlayerBadges = function(payload, cb) {
	logger.info('[dbRemote.getPlayerBadges] payload:', payload);
	dbm.getPlayerBadges(payload, cb);
};

/**
 * Lấy thông tin chi tiết của một thành tích
 * @param {Object} payload - Chứa badge_id và player_id
 * @param {Function} cb - Callback function
 */
DBRemote.prototype.getBadgeDetails = function(payload, cb) {
	logger.info('[dbRemote.getBadgeDetails] payload:', payload);
	dbm.getBadgeDetails(payload, cb);
};

/**
 * Đánh dấu thành tích đã nhận thưởng
 * @param {Object} payload - Chứa badge_id và player_id
 * @param {Function} cb - Callback function
 */
DBRemote.prototype.markBadgeAsClaimed = function(payload, cb) {
	logger.info('[dbRemote.markBadgeAsClaimed] payload:', payload);
	dbm.markBadgeAsClaimed(payload, cb);
};

/**
 * Thêm thành tích mới cho người chơi
 * @param {Object} payload - Chứa badge_id, player_id và các thông tin khác
 * @param {Function} cb - Callback function
 */
DBRemote.prototype.awardBadgeToPlayer = function(payload, cb) {
	logger.info('[dbRemote.awardBadgeToPlayer] payload:', payload);
	dbm.awardBadgeToPlayer(payload, cb);
};

/**
 * Tạo thành tích mới trong hệ thống
 * @param {Object} payload - Dữ liệu thành tích mới
 * @param {Function} cb - Callback function
 */
DBRemote.prototype.createBadge = function(payload, cb) {
	logger.info('[dbRemote.createBadge] payload:', payload);
	dbm.createBadge(payload, cb);
};

/**
 * Lấy danh sách tất cả các thành tích trong hệ thống
 * @param {Object} payload - Chứa các tham số lọc (type)
 * @param {Function} cb - Callback function
 */
DBRemote.prototype.getAllBadges = function(payload, cb) {
	logger.info('[dbRemote.getAllBadges] payload:', payload);
	dbm.getAllBadges(payload, cb);
};

/**
 * Lấy danh sách phần thưởng của một thành tích
 * @param {Object} payload - Chứa badge_id
 * @param {Function} cb - Callback function
 */
DBRemote.prototype.getBadgeRewards = function(payload, cb) {
	logger.info('[dbRemote.getBadgeRewards] payload:', payload);
	dbm.getBadgeRewards(payload, cb);
};

/**
 * Thêm phần thưởng mới cho thành tích
 * @param {Object} payload - Dữ liệu phần thưởng mới
 * @param {Function} cb - Callback function
 */
DBRemote.prototype.createBadgeReward = function(payload, cb) {
	logger.info('[dbRemote.createBadgeReward] payload:', payload);
	dbm.createBadgeReward(payload, cb);
};
//---------------------------- End: Badges ------------------------------------------------------------

//---------------------------- Begin: Level Ranks ------------------------------------------------------------
/**
 * Lấy danh sách tất cả các cấp độ
 * @param {Object} payload - Chứa các tham số lọc (nếu có)
 * @param {Function} cb - Callback function
 */
DBRemote.prototype.getAllLevelRanks = function(payload, cb) {
	logger.info('[dbRemote.getAllLevelRanks] payload:', payload);
	dbm.getAllLevelRanks(payload, cb);
};

/**
 * Lấy thông tin cấp độ theo level
 * @param {Object} payload - Chứa level
 * @param {Function} cb - Callback function
 */
DBRemote.prototype.getLevelRankByLevel = function(payload, cb) {
	logger.info('[dbRemote.getLevelRankByLevel] payload:', payload);
	dbm.getLevelRankByLevel(payload, cb);
};

/**
 * Lấy thông tin cấp độ tiếp theo của người chơi
 * @param {Object} payload - Chứa currentLevel
 * @param {Function} cb - Callback function
 */
DBRemote.prototype.getNextLevelRank = function(payload, cb) {
	logger.info('[dbRemote.getNextLevelRank] payload:', payload);
	dbm.getNextLevelRank(payload, cb);
};
//---------------------------- End: Level Ranks ------------------------------------------------------------

//---------------------------- Begin: Shop Items ------------------------------------------------------------
/**
 * Lấy danh sách vật phẩm trong shop
 * @param {Object} payload - Chứa các tham số lọc (type, category, currency)
 * @param {Function} cb - Callback function
 */
DBRemote.prototype.getShopItems = function(payload, cb) {
	logger.info('[dbRemote.getShopItems] payload:', payload);
	dbm.getShopItems(payload, cb);
};

/**
 * Lấy thông tin chi tiết một vật phẩm trong shop
 * @param {Object} payload - Chứa item_id
 * @param {Function} cb - Callback function
 */
DBRemote.prototype.getShopItemDetail = function(payload, cb) {
	logger.info('[dbRemote.getShopItemDetail] payload:', payload);
	dbm.getShopItemDetail(payload, cb);
};

/**
 * Mua vật phẩm trong shop
 * @param {Object} payload - Chứa player_id và item_id
 * @param {Function} cb - Callback function
 */
DBRemote.prototype.buyItem = function(payload, cb) {
	logger.info('[dbRemote.buyItem] payload:', payload);
	dbm.buyItem(payload, cb);
};

/**
 * Lấy lịch sử mua vật phẩm của người chơi
 * @param {Object} payload - Chứa player_id và các tham số lọc
 * @param {Function} cb - Callback function
 */
DBRemote.prototype.getShopPurchaseLogs = function(payload, cb) {
	logger.info('[dbRemote.getShopPurchaseLogs] payload:', payload);
	dbm.getShopPurchaseLogs(payload, cb);
};

/**
 * Tạo log mua vật phẩm
 * @param {Object} payload - Dữ liệu log
 * @param {Function} cb - Callback function
 */
DBRemote.prototype.createShopPurchaseLog = function(payload, cb) {
	logger.info('[dbRemote.createShopPurchaseLog] payload:', payload);
	dbm.createShopPurchaseLog(payload, cb);
};
//---------------------------- End: Shop Items ------------------------------------------------------------

//---------------------------- Begin: SNG Tournaments ------------------------------------------------------------
/**
 * Lấy số lượng người chơi đã đăng ký cho từng loại bàn SNG
 * @param {Function} cb - Callback function
 */
DBRemote.prototype.getRegisteredPlayersCount = function(cb) {
	logger.info('[dbRemote.getRegisteredPlayersCount]');
	dbm.getRegisteredPlayersCount(cb);
};

/**
 * Tìm bàn SNG còn trống để thêm người chơi
 * @param {Object} payload - Chứa table_type và level
 * @param {Function} cb - Callback function
 */
DBRemote.prototype.findAvailableSngTable = function(payload, cb) {
	logger.info('[dbRemote.findAvailableSngTable] payload:', payload);
	dbm.findAvailableSngTable(payload, cb);
};

/**
 * Tạo bàn SNG mới
 * @param {Object} payload - Dữ liệu bàn mới
 * @param {Function} cb - Callback function
 */
DBRemote.prototype.createSngTournament = function(payload, cb) {
	logger.info('[dbRemote.createSngTournament] payload:', payload);
	dbm.createSngTournament(payload, cb);
};

/**
 * Đăng ký người chơi vào giải đấu SNG
 * @param {Object} payload - Chứa tournament_id, player_id và thông tin khác
 * @param {Function} cb - Callback function
 */
DBRemote.prototype.registerPlayerToSngTournament = function(payload, cb) {
	logger.info('[dbRemote.registerPlayerToSngTournament] payload:', payload);
	dbm.registerPlayerToSngTournament(payload, cb);
};

/**
 * Cập nhật số dư của người chơi
 * @param {Object} playerId - ID người chơi
 * @param {Object} amount - Số tiền thay đổi (âm để trừ, dương để cộng)
 * @param {Object} description - Mô tả giao dịch
 * @param {Function} cb - Callback function
 */
DBRemote.prototype.updatePlayerBalance = function(playerId, amount, description, cb) {
	logger.info('[dbRemote.updatePlayerBalance] playerId:', playerId, 'amount:', amount);
	dbm.updatePlayerBalance(playerId, amount, description, cb);
};

/**
 * Lấy thông tin giải đấu SNG theo ID
 * @param {number} tournamentId - ID giải đấu
 * @param {Function} cb - Callback function
 */
DBRemote.prototype.getSngTournamentById = function(tournamentId, cb) {
	logger.info('[dbRemote.getSngTournamentById] tournamentId:', tournamentId);
	dbm.getSngTournamentById(tournamentId, cb);
};

/**
 * Đếm số người chơi còn lại (ACTIVE) trong giải đấu SNG
 * @param {number} tournamentId - ID giải đấu
 * @param {Function} cb - Callback function
 */
DBRemote.prototype.countActiveSngTournamentPlayers = function(tournamentId, cb) {
	logger.info('[dbRemote.countActiveSngTournamentPlayers] tournamentId:', tournamentId);
	dbm.countActiveSngTournamentPlayers(tournamentId, cb);
};

/**
 * (đã convert sang dbService)
 * Cập nhật trạng thái người chơi trong giải đấu SNG
 * @param {Object} payload - Dữ liệu cập nhật
 * @param {Function} cb - Callback function
 */
// DBRemote.prototype.updateSngTournamentPlayer = function(payload, cb) {
// 	logger.info('[dbRemote.updateSngTournamentPlayer] payload:', payload);
// 	dbm.updateSngTournamentPlayer(payload, cb);
// };

/**
 * (đã convert sang dbService)
 * Ghi log hành động trong giải đấu SNG
 * @param {Object} payload - Dữ liệu log
 * @param {Function} cb - Callback function
 */
// DBRemote.prototype.createSngTournamentLog = function(payload, cb) {
// 	logger.info('[dbRemote.createSngTournamentLog] payload:', payload);
// 	dbm.createSngTournamentLog(payload, cb);
// };

/**
 * (đã convert sang dbService)
 * Cập nhật trạng thái giải đấu SNG
 * @param {Object} payload - Dữ liệu cập nhật
 * @param {Function} cb - Callback function
 */
// DBRemote.prototype.updateSngTournament = function(payload, cb) {
// 	logger.info('[dbRemote.updateSngTournament] payload:', payload);
// 	dbm.updateSngTournament(payload, cb);
// };

/**
 * (đã convert sang dbService)
 * Tạo tournament trong database
 * @param {Object} payload - Dữ liệu tournament
 * @param {Function} cb - Callback function
 */
// DBRemote.prototype.createSngTournament = function(payload, cb) {
// 	logger.info('[dbRemote.createSngTournament] payload:', payload);
// 	dbm.createSngTournament(payload, cb);
// };

/**
 * (đã convert sang dbService)
 * Tạo tournament player trong database
 * @param {Object} payload - Dữ liệu tournament player
 * @param {Function} cb - Callback function
 */
// DBRemote.prototype.createSngTournamentPlayer = function(payload, cb) {
// 	logger.info('[dbRemote.createSngTournamentPlayer] payload:', payload);
// 	dbm.createSngTournamentPlayer(payload, cb);
// };
//---------------------------- End: SNG Tournaments ------------------------------------------------------------

