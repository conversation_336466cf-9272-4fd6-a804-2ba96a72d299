var logger      = require('pomelo-logger').getLogger('game-log', __filename);
var consts      = require('../../../consts/consts');
var schedule    = require('pomelo-scheduler');
var _           = require('underscore');

module.exports = function(app){
    return new Remote(app);
};
var Remote = function(app){
    this.app = app;
    this.tableService = app.get('sngTableService');
    this.jobQueue = [];
    this.jobClearQueue = [];
    this.jobStartQueue = [];

    // Initialize global channel for system-wide notifications
    var channelService = app.get('channelService');
    var globalChannel = channelService.createChannel('global');
    app.set('globalChannel', globalChannel);
};
var remote = Remote.prototype;

/**
 * Remove member/player from table
 *
 * @param {string} uid user id
 * @param {string} sid server id
 * @param {string} tid channel id
 * @param {function} cb callback
 *
 */
remote.removeMember = function(uid, sid, tid, cb){
    this.tableService.removeMember(tid, uid, cb);
};

remote.removeMember2 = function(tid, uid, cb){
    this.tableService.removeMember(tid, uid, cb);
};

remote.getTable = function (tid, cb) {
    // var arr = true;
    logger.info("TableRemote >> getTable with tid ", tid);
    var table = this.tableService.getTableDetail(tid);
    logger.info("TableRemote >> getTable >> table: ", table);
    // if (!table.status) {
    //     arr = false;
    // }
    cb(table);
};

/**
 * Hàm remote remove player to playersToRemove in table
 * @param uid
 * @param tid
 * @param cb
 */
remote.removePlayer = function (tid, uid, cb) {
    this.tableService.removePlayerFromRemote(tid, uid, cb);
};


/**
 * remove start game
 * @param tid
 * @param cb
 */
remote.startGame = function (tid, cb) {
    logger.info("[tableRemote] startGame from tableRemote with tableId: ", tid);
    this.tableService.startGame(tid, cb);
};

remote.pushTableClear = function (tid, cb) {
    logger.info("[tableRemote] pushTableClear from table ", tid);
    this.tableService.pushTableClear(tid, cb);
};

// Khong su dung
remote.addJobStartGameOld = function (tid, timer, cb) {

    var self = this;
    logger.info("[addJobStartGame] in time: ", timer , " => ", Date.now() + timer * 1000, " and tid ", tid);
    var jobId = schedule.scheduleJob({start: Date.now() + timer * 1000}, function (jobData) {

        self.startGame(tid, function (e) {
            logger.info("StartGame from tableRemote -------------- with jobData: ", jobData, " => jobId: ", jobId);
            if(e){
                return logger.error('tableRemote => can not start game yet with jobId', jobData ," e: ", e);
            }

            logger.info("self.jobQueue in addJobStartGame truoc: ", self.jobQueue);

            self.jobQueue = _.filter(self.jobQueue, function (j) {
                logger.info("j = ", j);
                var str1    = j.tid;
                var str2    = jobData.tid;
                var n       = str1.localeCompare(str2);
                logger.info("str1: ", str1, " str2: ", str2, " => n: ", n);
                return n != -1;
                //return j.tid != jobData.tid;
            });

            logger.info("self.jobQueue in addJobStartGame sau: ", self.jobQueue);
            schedule.cancelJob(jobId);

        });

        self.clearJobStartGame(tid, function () {});

    }, {tid: tid});

    logger.info(" addJobStartGame with tid ",tid," and jobId ", jobId," ----------------------");

    self.clearJobStartGame(tid, function () {});

    this.jobQueue.push({tid: tid, jobId: jobId, action: "start"});
    logger.info("-------- addJobStartGame sau khi add: ", this.jobQueue);
    cb();
};
// Khong su dung
remote.addJobClearGameOld = function (tid, timer, cb) {

    var self = this;
    //logger.info("addJobClearGame in time: ", Date.now() + timer * 1000);
    var jobId = schedule.scheduleJob({start: Date.now() + timer * 1000}, function (jobData) {

        self.pushTableClear(tid, function (e) {
            logger.info("[tableRemote] ->sau khi send command clear thanh cong !");
        });
        logger.info("addJobClearGame => jobData: ", jobData);
        logger.info("self.jobQueue in addJobClearGame truoc khi clear : ", self.jobQueue);

        self.jobQueue = _.filter(self.jobQueue, function (j) {
            //return j.tid != jobData.tid && j.action != "clear";
            var str1 = j.tid;
            var str2 = jobData.tid;
            var n = str1.localeCompare(str2);
            return n != -1 && j.action != "clear";
        });

        logger.info("self.jobQueue in addJobClearGame sau khi da clear : ", self.jobQueue);
        logger.info("in addJobClearGame cancelJob id ", jobId);
        //schedule.cancelJob(jobId);

    }, {tid: tid});

    logger.info(" addJobClearGame with tid ",tid," and jobId ", jobId," ----------------------");

    this.jobQueue.push({tid: tid, jobId: jobId, action: "clear"});
    logger.info("-------- addJobClearGame sau khi add: ", this.jobQueue);
    cb();
};
// Khong su dung
remote.clearJobStartGameOld = function (tid, cb) {
    logger.info("clearJobStartGame with table ",tid," and this.jobQueue truoc: ", this.jobQueue);
    //var job = _.findWhere(this.jobQueue, {tid: tid});
    var job = _.findWhere(this.jobQueue, {tid: tid, action: "start"});
    logger.info("jobFind => ", job);
    if (!!job) {
        schedule.cancelJob(job.jobId);
        this.jobQueue = _.filter(this.jobQueue, function (j) {
            return j.jobId != job.jobId;
            //return j.jobId != job.jobId && j.action != "clear";
        });
    }
    logger.info("clearJobStartGame with tid ",tid," and jobId: ", job, "----------------------");
    logger.info("clearJobStartGame with this.jobQueue sau: ", this.jobQueue);
    cb();
};



/**
 * queue tự động start game
 * @param tid
 * @param timer
 * @param cb
 * @lastupdate
 * - [fix] check xem với tid đó đã có job nào đang đợi chưa, nếu có rồi thì thôi ko tạo nữa, bug cũ là vẫn tạo xong mới check
 */
remote.addJobStartGame = function (tid, timer, cb) {

    var self = this;
    logger.info("[addJobStartGame][step 1] in time: ", timer , " => ", Date.now() + timer * 1000, " and tid ", tid);
    // var jobId = schedule.scheduleJob({start: Date.now() + timer * 1000}, function (jobData) {
    //
    //     self.startGame(tid, function (e) {
    //         logger.info("[addJobStartGame] Auto Start Game tu tableRemote with jobData: ", jobData, " => jobId: ", jobId);
    //         if(e){
    //             return logger.debug('tableRemote => cant start game yet with jobId', jobData ," e: ", e);
    //         }
    //         logger.info("[addJobStartGame] >> jobStartQueue truoc khi filter: ", self.jobStartQueue);
    //         self.jobStartQueue = _.filter(self.jobStartQueue, function (j) {
    //             return j.tid != jobData.tid;
    //         });
    //     });
    //
    // }, {tid: tid});
    //
    // logger.info("[addJobStartGame] with tid ",tid," and jobId ", jobId);

    var findJobByTid = _.findWhere(this.jobStartQueue, {tid: tid});
    if (!findJobByTid) {
        // Không có thì mới tạo job
        // ------------------------------------------------------------------------------------------------------------
        // logger.info("[addJobStartGame] in time: ", timer , " => ", Date.now() + timer * 1000, " and tid ", tid);
        logger.info("[addJobStartGame][step 2] findJobByTid: ", findJobByTid, " => create new job with tid: ", tid);
        var jobId = schedule.scheduleJob({start: Date.now() + timer * 1000}, function (jobData) {

            self.startGame(tid, function (e) {
                logger.info("[addJobStartGame][step 3] Auto Start Game tu tableRemote with jobData: ", jobData, " => jobId: ", jobId);
                if(e){
                    // return logger.debug('tableRemote => cant start game yet with jobId', jobData ," e: ", e);
                    logger.debug('tableRemote => can not start game yet with jobId', jobData ," e: ", e);
                }
                logger.info("[addJobStartGame][step 4] >> jobStartQueue truoc khi filter: ", self.jobStartQueue);
                /* self.jobStartQueue = _.filter(self.jobStartQueue, function (j) {
                    return j.tid != jobData.tid;
                }); */
                self.jobStartQueue = _.filter(self.jobStartQueue, function (j) {
                    logger.info("[addJobStartGame][step 4] j = ", j);
                    var str1    = j.tid;
                    var str2    = jobData.tid;
                    var n       = str1.localeCompare(str2);
                    logger.info("[addJobStartGame][step 4] str1: ", str1, " str2: ", str2, " => n: ", n);
                    return n != -1;
                    //return j.tid != jobData.tid;
                });

                logger.info("[addJobStartGame][step 5] >> jobStartQueue sau khi filter: ", self.jobStartQueue);
                schedule.cancelJob(jobId);
            });

            self.clearJobStartGame(tid, function () {});

        }, {tid: tid});

        logger.info("[addJobStartGame][step 6] with tid ",tid," and jobId ", jobId);

        self.clearJobStartGame(tid, function () {});

        // Push vào queue
        // ------------------------------------------------------------------------------------------------------------
        self.jobStartQueue.push({tid: tid, jobId: jobId});
        logger.info("[addJobStartGame][step 7] >> sau khi duoc add jobId ", jobId ," voi tid:  ", tid ," => jobStartQueue: ", self.jobStartQueue);
    } else {
        logger.info("[addJobStartGame][step 8] >> khong can add vi da co roi : ", findJobByTid);
    }

    cb();
};


/**
 * queue tự động gửi command clear bàn trước  khi ván mới bắt đầu
 * @param tid
 * @param timer
 * @param cb
 * @lastupdate
 * - [fix] check xem có job clear của tid đó chưa rồi mới thực hiện chạy job, bug cũ là tạo rồi mới check
 */
remote.addJobClearGame = function (tid, timer, cb) {
    logger.info("[addJobClearGame] from table ", tid, " with timer ", timer);
    var self = this;

    // var jobId = schedule.scheduleJob({start: Date.now() + timer * 1000}, function (jobData) {
    //     logger.info("[addJobClearGame][InProcess] Start processing send command clear to members in table");
    //     self.pushTableClear(tid, function (e) {
    //         logger.info("[addJobClearGame][InProcess] -> sau khi send command clear thanh cong !");
    //     });
    //     logger.info("[addJobClearGame][InProcess] jobData: ", jobData);
    //     logger.info("[addJobClearGame][InProcess] jobClearQueue before clear: ", self.jobClearQueue);
    //
    //     self.jobClearQueue = _.filter(self.jobClearQueue, function (j) {
    //         return j.tid != jobData.tid;
    //     });
    //
    //     logger.info("[addJobClearGame][InProcess] jobClearQueue after clear: ", self.jobClearQueue);
    //     logger.info("[addJobClearGame][InProcess] jobId: ", jobId);
    //
    // }, {tid: tid});
    //
    // logger.info("[addJobClearGame] from table ",tid," and jobId created ", jobId," ----------------------");


    // var findJobByTid = _.findWhere(this.jobClearQueue, {tid: tid});
    // if (!findJobByTid) {

        // Create job
        // ------------------------------------------------------------------------------------------------------------
        var jobId = schedule.scheduleJob({start: Date.now() + timer * 1000}, function (jobData) {
            logger.info("[addJobClearGame][InProcess] Start processing send command clear to members in table");
            self.pushTableClear(tid, function (e) {
                logger.info("[addJobClearGame][InProcess] -> sau khi send command clear thanh cong !");
            });
            logger.info("[addJobClearGame][InProcess] jobData: ", jobData);
            logger.info("[addJobClearGame][InProcess] jobClearQueue before clear: ", self.jobClearQueue);

            self.jobClearQueue = _.filter(self.jobClearQueue, function (j) {
                return j.tid !== jobData.tid;
                // var str1 = j.tid;
                // var str2 = jobData.tid;
                // var n = str1.localeCompare(str2);
                // logger.info("[addJobClearGame][InProcess] str1: ", str1, " str2: ", str2, " => n: ", n);
                // return n != -1;
            });

            logger.info("[addJobClearGame][InProcess] jobClearQueue after filter: ", self.jobClearQueue);
            logger.info("[addJobClearGame][InProcess] jobId: ", jobId);

        }, {tid: tid});

        logger.info("[addJobClearGame] from table ",tid," and jobId created is ", jobId," ----------------------");

        // Push to queue
        // ------------------------------------------------------------------------------------------------------------
        this.jobClearQueue.push({tid: tid, jobId: jobId});
        logger.info("[addJobClearGame] jobClearQueue after add to list: ", this.jobClearQueue);
    // }else{
    //     logger.info("[addJobClearGame][Đã có job] khong can add vi da co roi : ", findJobByTid);
    // }

    cb();
};

remote.clearJobStartGame = function (tid, cb) {
    logger.info("[clearJobStartGame][step 1] from table id: ",tid);
    logger.info("[clearJobStartGame][step 2] jobStartQueue truoc khi clear: ", this.jobStartQueue);
    var job = _.findWhere(this.jobStartQueue, {tid: tid});
    logger.info("[clearJobStartGame][step 3] jobFindStartGame: ", job);
    if (!!job) {
        schedule.cancelJob(job.jobId);
        this.jobStartQueue = _.filter(this.jobStartQueue, function (j) {
            return j.jobId != job.jobId;
        });
    }
    logger.info("[clearJobStartGame][step 4] from table id: ",tid," and job ", job);
    logger.info("[clearJobStartGame][step 5] jobStartQueue sau khi clear: ", this.jobStartQueue);

    cb();
};