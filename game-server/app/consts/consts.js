module.exports = {
    GAME: {
        TYPE: {
            FIVE: 5,
            NINE: 9
        },
        MODE: {
            NORMAL: 'normal',
            FAST: 'fast',
            SNG: 'sng'
        },
        LOGIN: {
            REGISTER: 1,
            FACEBOOK: 2,
            DEVICES: 3
        },
        BALANCE: 1000000,
        LOGINHELP: 10000, // giá trị cứu trợ login
        CASH: {
            FB: 5000000, // thưởng khi đăng ký tài khoản bằng facebook
            DEVICE:3000000, // thưởng khi đăng ký tài khoản bằng device
            REGISTER: 1000000, // thưởng khi đăng ký tài khoản bằng web
            GOOGLE: 5000000, // thưởng khi đăng ký tài khoản bằng google
        },
        TIMER: {
            TALK: 60,
            WAIT_START: 8, // 7, // 9, // Thời gian chờ khi có từ 2 người chơi lên để tự động chia bài
            WAIT_CLEAR: 6, //5, // 7,
            FIRST_START: 4,
            NOT_ACTION: 400, // thời gian chời ko làm gì cẩ thì bị kick ra ngoài
            SNG_COUNTDOWN: 5 // Thời gian đếm ngược khi bắt đầu giải đấu SNG
        },
        ACTOR_RESULT: {
            WIN: 'WIN',
            LOSE: 'LOSE'
        },
        ROUTER: {
            // JOIN_TABLE: "JoinTable",
            JOIN_TABLE: "onJoinTable",
            // JOIN_GAME: "onTableJoin",
            JOIN_GAME: "onJoinGame",
            GAME_EVENT: "onTableEvent",
            END_TURN: "onEndTurn", // action cuối cùng gửi về client trước khi gửi command END_GAME
            END_GAME: "onEndGame", // command gửi thông tin đầy đủ khi kết thúc game (bao gồm gameWinners, sidepots, ...)
            UPDATE_USERS: "onUpdateUsers",
            UPDATE_MYSELF: "onUpdateMyself",
            GAME_CLEAR: "onClearTable", // command thông báo client clear hết các thông tin từ ván trước để bắt đầu ván mới
            KICK_USER: "onKickUser", // command kích user ra khỏi game về lý do gì đó
            INVITE_PLAY: "onInvitePlay", // command nhận mời chơi từ bạn bè gửi đến
            NOTIFICATION: 'onNotification', // command gửi notification đến client
            ON_FRIENDS: 'onFriends', // command gửi/nhận yêu cầu kết bạn từ bạn bè gửi đến
            ON_CHAT: 'onChat', // command gửi/nhận tin nhắn chat từ bạn bè gửi đến
            ON_USER_CHAT: 'onUserChat', // command gửi/nhận tin nhắn chat từ bạn bè gửi đến

            // SNG Tournament events
            SNG_TOURNAMENT_LIST: 'onSngTournamentList', // Danh sách giải đấu SNG
            SNG_TOURNAMENT_JOIN: 'onSngTournamentJoin', // Tham gia giải đấu SNG
            SNG_TOURNAMENT_STATUS: 'onSngTournamentStatus', // Cập nhật trạng thái giải đấu SNG
            SNG_TOURNAMENT_UPDATE: 'onSngTournamentUpdate', // Cập nhật chung cho giải đấu SNG
            SNG_TOURNAMENT_BLIND_UPDATE: 'onSngBlindUpdate', // Cập nhật mức blind trong giải đấu SNG
            SNG_TOURNAMENT_RESULT: 'onSngTournamentResult', // Kết quả giải đấu SNG
            SNG_TOURNAMENT_PLAYER_ELIMINATED: 'onSngPlayerEliminated', // Thông báo người chơi bị loại
            SNG_TOURNAMENT_ENDED: 'onSngTournamentEnded' // Thông báo giải đấu kết thúc
        },
        LOGS: {
            EX: "logs_game", // exchangeName của rabbitmq
            FRIENDS: "friends", // exchangeName của rabbitmq cho phần bạn bè
            SNG: "sng_tournaments" // exchangeName của rabbitmq cho phần giải đấu SNG
        }
    },
    CACHE: {
        ONLINE_USERS: 'online_users'
    },
    EVENT: {
        MAX_SPIN: 2, // max lượt quay
        COUNT_TIME_SPIN: 2, //4, // 4 giờ
        MAX_HELP: 5, //3, // max lần cứu trợ
        PAGESIZE: 5 // số bạn trên 1 page
    },
    MESSAGE: {
        DISCONNECT: "Connection lost. Try to reconnect.",
        DUPLICATE: "Someone has logged into your account",
        LONG_TIME: "You have been kicked out because of no interaction in a long time",
        NOT_ENOUGH_MONEY: "You haven't enough money!"
    },
    MODULE: {
        EVENT: "events",
        GIFTCODE: "giftcode",
        TIPDEALER: "TIPDEALER",
        GIFTCARD: "giftcard"
    },
    TIPDEALER: {
        CHIP: 'TIP_CHIP', // tip chips
        PROPS: 'TIP_PROPS' // tip vat pham
    },
    API: {
        URL: "http://127.0.0.1:8200",
        GIFTCARD: "http://127.0.0.1:8200",
        CLIENT_ID: ***************
    },
    LOG: {
        GAME: 'game.log',
        USER: 'user.log',
        GOLD: 'gold.log',
        SETTLE: 'settle.log'
    },
    LOGIN_TYPE: {
        DEVICE: "DEVICE",
        FACEBOOK: "FACEBOOK",
        APPLE: "APPLE",
        GOOGLE: "GOOGLE",
        WEB: "WEB",
        EMAIL: "EMAIL",
    },
    // mảng app_id của các app định nghĩa đồng tiền khác ngoài chips
    APPS_BALANCE: {
        COIN: 1,
        POKEE: 2,
        DIAMOND: 3,
    }
};