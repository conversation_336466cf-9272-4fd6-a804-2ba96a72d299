/**
 * Test script để kiểm tra logic SNG tournament initialization
 */

// Mock dependencies
const uuid = require('node-uuid');
const _ = require('underscore');

// Mock logger
const logger = {
    info: console.log,
    error: console.error,
    warn: console.warn
};

// Mock sngTournaments config
const sngTournaments = {
    tournaments: [
        {
            id: "sng_3_test",
            name: "SNG 3 Players - TEST",
            player_capacity: 3,
            buy_in: 1000000,
            fee: 100000,
            level: "TEST",
            initial_chips: 10000000,
            blind_duration_minutes: 3
        },
        {
            id: "sng_5_beginner",
            name: "SNG 5 Players - Beginner",
            player_capacity: 5,
            buy_in: 50000000,
            fee: 5000000,
            level: "BEGINNER",
            initial_chips: 100000000,
            blind_duration_minutes: 5
        },
        {
            id: "sng_5_intermediate",
            name: "SNG 5 Players - Intermediate",
            player_capacity: 5,
            buy_in: 100000000,
            fee: 10000000,
            level: "INTERMEDIATE",
            initial_chips: 100000000,
            blind_duration_minutes: 5
        },
        {
            id: "sng_9_beginner",
            name: "SNG 9 Players - Beginner",
            player_capacity: 9,
            buy_in: 50000000,
            fee: 5000000,
            level: "BEGINNER",
            initial_chips: 100000000,
            blind_duration_minutes: 5
        }
    ],
    blind_structure: [
        { level: 1, small_blind: 500000, big_blind: 1000000, ante: 0 },
        { level: 2, small_blind: 1000000, big_blind: 2000000, ante: 0 }
    ],
    reward_distribution: {
        first_place: 50,
        second_place: 30,
        third_place: 20
    }
};

// Mock SNG Table Service
class MockSngTableService {
    constructor() {
        this.waitingTournaments = {};
        this.tables = {};
        this.tournaments = {};
        
        // Initialize
        this.init();
    }
    
    init() {
        logger.info("---> Khoi tao SNG Tournament Service");
        this.autoInitTournamentsPerType();
    }
    
    autoInitTournamentsPerType() {
        logger.info("---> Auto init SNG tournaments per type/level (in-memory only)");
        
        // Initialize one waiting tournament for each configuration
        sngTournaments.tournaments.forEach((config) => {
            var tableType = config.player_capacity === 5 ? '5_PLAYERS' : 
                           (config.player_capacity === 9 ? '9_PLAYERS' : '3_PLAYERS');
            var level = config.level;
            var tournamentKey = tableType + '_' + level;
            
            // Create waiting tournament (in-memory only)
            this.createInMemoryWaitingTournament(config, tableType, level);
            logger.info("Created in-memory waiting tournament for", tournamentKey);
        });
    }
    
    createInMemoryWaitingTournament(config, tableType, level) {
        var tournamentKey = tableType + '_' + level;

        // Check if waiting tournament already exists
        if (this.waitingTournaments[tournamentKey]) {
            return this.waitingTournaments[tournamentKey];
        }

        var tournamentId = uuid.v1();
        var tableId = uuid.v1();

        logger.info("[createInMemoryWaitingTournament] Creating in-memory waiting tournament:", tournamentKey, "tournament_id:", tournamentId);

        // Create tournament data for memory only (no database yet)
        var tournament = {
            id: tournamentId, // Temporary ID, will be replaced when saved to database
            table_type: tableType,
            level: level,
            config: config,
            players: [], // Empty initially
            status: 'waiting',
            created_at: Date.now(),
            started_at: null,
            finished_at: null,
            current_blind_level: 0,
            blind_schedule_job: null,
            elimination_log: [],
            registered_count: 0,
            db_tournament: null // Will be set when saved to database
        };

        // Store as waiting tournament
        this.waitingTournaments[tournamentKey] = tournament;

        // Create table object for this waiting tournament
        var tableObj = {
            id: tableId,
            tournament_id: tournamentId, // Use temporary ID
            creator: null, // Will be set when first player joins
            state: 'WAITING'
        };

        this.tables[tableId] = tableObj;
        tournament.table_id = tableId;

        return tournament;
    }
    
    findTournamentConfig(tableType, level) {
        var playerCapacity = tableType === '5_PLAYERS' ? 5 : 
                            (tableType === '9_PLAYERS' ? 9 : 3);
        
        return sngTournaments.tournaments.find(function(config) {
            return config.player_capacity === playerCapacity && config.level === level;
        });
    }
    
    getRegisteredPlayersCount(tableType, level) {
        var tournamentKey = tableType + '_' + level;
        var waitingTournament = this.waitingTournaments[tournamentKey];
        
        if (!waitingTournament) {
            return 0;
        }
        
        return waitingTournament.registered_count || 0;
    }
    
    // Test method to simulate player registration
    simulatePlayerRegistration(tableType, level) {
        var tournamentKey = tableType + '_' + level;
        var tournament = this.waitingTournaments[tournamentKey];
        
        if (tournament) {
            tournament.registered_count++;
            logger.info(`[simulatePlayerRegistration] Player registered for ${tournamentKey}. Count: ${tournament.registered_count}/${tournament.config.player_capacity}`);
            
            // Check if tournament needs to be saved to database
            if (!tournament.db_tournament) {
                logger.info(`[simulatePlayerRegistration] Tournament ${tournamentKey} would be saved to database now`);
                // In real implementation, this would call database save
                tournament.db_tournament = { id: 'db_' + tournament.id, saved: true };
            }
            
            return tournament;
        }
        
        return null;
    }
}

// Test the logic
console.log("=== Testing SNG Tournament Logic ===\n");

const sngService = new MockSngTableService();

console.log("\n=== Initial State ===");
console.log("Waiting tournaments:", Object.keys(sngService.waitingTournaments));

console.log("\n=== Testing Registration Counts ===");
console.log("5_PLAYERS BEGINNER count:", sngService.getRegisteredPlayersCount('5_PLAYERS', 'BEGINNER'));
console.log("9_PLAYERS BEGINNER count:", sngService.getRegisteredPlayersCount('9_PLAYERS', 'BEGINNER'));
console.log("3_PLAYERS TEST count:", sngService.getRegisteredPlayersCount('3_PLAYERS', 'TEST'));

console.log("\n=== Testing Player Registration ===");
sngService.simulatePlayerRegistration('5_PLAYERS', 'BEGINNER');
sngService.simulatePlayerRegistration('5_PLAYERS', 'BEGINNER');
sngService.simulatePlayerRegistration('3_PLAYERS', 'TEST');

console.log("\n=== Final State ===");
Object.keys(sngService.waitingTournaments).forEach(key => {
    const tournament = sngService.waitingTournaments[key];
    console.log(`${key}: ${tournament.registered_count}/${tournament.config.player_capacity} players, DB saved: ${!!tournament.db_tournament}`);
});

console.log("\n=== Test Completed ===");
