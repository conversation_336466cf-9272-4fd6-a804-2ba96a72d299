/**
 * Test script để kiểm tra logic tính thứ hạng SNG tournament
 */

// Test logic tính thứ hạng
function testRankingLogic() {
    console.log("=== Testing SNG Tournament Ranking Logic ===\n");

    // Scenario 1: Bàn 3 người
    console.log("--- Scenario 1: Bàn 3 người ---");
    
    // Ban đầu: 3 người chơi
    var playersToAdd = [];
    var playersInGame = [
        { id: 1, chips: 5000000 },
        { id: 2, chips: 3000000 },
        { id: 3, chips: 2000000 }
    ];
    
    console.log("Ban đầu:");
    console.log("- playersToAdd:", playersToAdd.length);
    console.log("- playersInGame:", playersInGame.length);
    console.log("- Total players:", playersToAdd.length + playersInGame.length);
    
    // Player 3 bị loại (chips = 0)
    playersInGame[2].chips = 0;
    var playersInGameActive = playersInGame.filter(p => p.chips > 0);
    var totalPlayersRemaining = playersToAdd.length + playersInGameActive.length;
    var playerRank = totalPlayersRemaining + 1; // Rank của player 3 (FIXED)
    
    console.log("\nPlayer 3 bị loại:");
    console.log("- playersToAdd:", playersToAdd.length);
    console.log("- playersInGame (active):", playersInGameActive.length);
    console.log("- Total remaining:", totalPlayersRemaining);
    console.log("- Player 3 rank:", playerRank, "(Expected: 3)");
    
    // Player 2 bị loại
    playersInGame[1].chips = 0;
    playersInGameActive = playersInGame.filter(p => p.chips > 0);
    totalPlayersRemaining = playersToAdd.length + playersInGameActive.length;
    playerRank = totalPlayersRemaining + 1; // Rank của player 2 (FIXED)
    
    console.log("\nPlayer 2 bị loại:");
    console.log("- playersToAdd:", playersToAdd.length);
    console.log("- playersInGame (active):", playersInGameActive.length);
    console.log("- Total remaining:", totalPlayersRemaining);
    console.log("- Player 2 rank:", playerRank, "(Expected: 2)");
    
    // Player 1 thắng
    console.log("\nPlayer 1 thắng:");
    console.log("- playersToAdd:", playersToAdd.length);
    console.log("- playersInGame (active):", playersInGameActive.length);
    console.log("- Total remaining:", totalPlayersRemaining);
    console.log("- Player 1 rank: 1 (Winner)");
    
    console.log("\n--- Scenario 2: Bàn 5 người ---");
    
    // Reset cho bàn 5 người
    playersToAdd = [];
    playersInGame = [
        { id: 1, chips: 8000000 },
        { id: 2, chips: 6000000 },
        { id: 3, chips: 4000000 },
        { id: 4, chips: 2000000 },
        { id: 5, chips: 1000000 }
    ];
    
    console.log("Ban đầu:");
    console.log("- Total players:", playersToAdd.length + playersInGame.length);
    
    // Player 5 bị loại
    playersInGame[4].chips = 0;
    playersInGameActive = playersInGame.filter(p => p.chips > 0);
    totalPlayersRemaining = playersToAdd.length + playersInGameActive.length;
    playerRank = totalPlayersRemaining + 1; // FIXED
    
    console.log("\nPlayer 5 bị loại:");
    console.log("- Total remaining:", totalPlayersRemaining);
    console.log("- Player 5 rank:", playerRank, "(Expected: 5)");
    
    // Player 4 bị loại
    playersInGame[3].chips = 0;
    playersInGameActive = playersInGame.filter(p => p.chips > 0);
    totalPlayersRemaining = playersToAdd.length + playersInGameActive.length;
    playerRank = totalPlayersRemaining + 1; // FIXED

    console.log("\nPlayer 4 bị loại:");
    console.log("- Total remaining:", totalPlayersRemaining);
    console.log("- Player 4 rank:", playerRank, "(Expected: 4)");

    // Player 3 bị loại
    playersInGame[2].chips = 0;
    playersInGameActive = playersInGame.filter(p => p.chips > 0);
    totalPlayersRemaining = playersToAdd.length + playersInGameActive.length;
    playerRank = totalPlayersRemaining + 1; // FIXED

    console.log("\nPlayer 3 bị loại:");
    console.log("- Total remaining:", totalPlayersRemaining);
    console.log("- Player 3 rank:", playerRank, "(Expected: 3) - TOP 3!");

    // Player 2 bị loại
    playersInGame[1].chips = 0;
    playersInGameActive = playersInGame.filter(p => p.chips > 0);
    totalPlayersRemaining = playersToAdd.length + playersInGameActive.length;
    playerRank = totalPlayersRemaining + 1; // FIXED
    
    console.log("\nPlayer 2 bị loại:");
    console.log("- Total remaining:", totalPlayersRemaining);
    console.log("- Player 2 rank:", playerRank, "(Expected: 2) - TOP 2!");
    
    // Player 1 thắng
    console.log("\nPlayer 1 thắng:");
    console.log("- Total remaining:", totalPlayersRemaining);
    console.log("- Player 1 rank: 1 (Winner) - TOP 1!");
    
    console.log("\n--- Scenario 3: Kiểm tra tournament kết thúc ---");
    
    // Scenario khi chỉ còn 1 người
    playersToAdd = [{ id: 1, chips: 10000000 }]; // 1 người trong playersToAdd
    playersInGame = []; // Không có ai trong game
    
    totalPlayersRemaining = playersToAdd.length + playersInGame.length;
    console.log("Chỉ còn 1 người:");
    console.log("- playersToAdd:", playersToAdd.length);
    console.log("- playersInGame:", playersInGame.length);
    console.log("- Total remaining:", totalPlayersRemaining);
    console.log("- Tournament should end:", totalPlayersRemaining === 1 ? "YES" : "NO");
    
    // Scenario khác: 1 người trong game, 0 trong playersToAdd
    playersToAdd = [];
    playersInGame = [{ id: 1, chips: 10000000 }];
    
    totalPlayersRemaining = playersToAdd.length + playersInGame.filter(p => p.chips > 0).length;
    console.log("\nChỉ còn 1 người (trong game):");
    console.log("- playersToAdd:", playersToAdd.length);
    console.log("- playersInGame (active):", playersInGame.filter(p => p.chips > 0).length);
    console.log("- Total remaining:", totalPlayersRemaining);
    console.log("- Tournament should end:", totalPlayersRemaining === 1 ? "YES" : "NO");
}

// Test logic tính phần thưởng
function testRewardCalculation() {
    console.log("\n=== Testing Reward Calculation ===\n");
    
    var tournament = {
        id: 1,
        reward_pool: 1000000000 // 1 tỷ chips
    };
    
    var rewardDistribution = {
        first_place: 50,   // 50%
        second_place: 30,  // 30%
        third_place: 20    // 20%
    };
    
    function calculateRewardAmount(tournament, rank) {
        if (!tournament || !tournament.reward_pool || rank > 3) {
            return 0;
        }
        
        var percentage = 0;
        switch (rank) {
            case 1:
                percentage = rewardDistribution.first_place;
                break;
            case 2:
                percentage = rewardDistribution.second_place;
                break;
            case 3:
                percentage = rewardDistribution.third_place;
                break;
            default:
                percentage = 0;
        }
        
        return Math.floor((tournament.reward_pool * percentage) / 100);
    }
    
    console.log("Tournament reward pool:", tournament.reward_pool.toLocaleString(), "chips");
    console.log("Rank 1 reward:", calculateRewardAmount(tournament, 1).toLocaleString(), "chips (50%)");
    console.log("Rank 2 reward:", calculateRewardAmount(tournament, 2).toLocaleString(), "chips (30%)");
    console.log("Rank 3 reward:", calculateRewardAmount(tournament, 3).toLocaleString(), "chips (20%)");
    console.log("Rank 4 reward:", calculateRewardAmount(tournament, 4).toLocaleString(), "chips (0%)");
}

// Chạy tests
testRankingLogic();
testRewardCalculation();

console.log("\n=== Test Completed ===");
